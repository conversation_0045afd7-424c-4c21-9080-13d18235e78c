{"name": "smart-aio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "npx prisma generate", "db:migrate": "npx prisma migrate dev", "db:push": "npx prisma db push --accept-data-loss", "db:reset": "npx prisma migrate reset --force && npm run db:sync && npm run db:init", "db:sync": "npx prisma db push --accept-data-loss", "db:init": "node scripts/init-database.js", "db:seed": "node scripts/seed-qa-test-data.js", "db:full-reset": "npm run db:reset && npm run db:seed", "db:setup": "npm run db:sync && npm run db:init && npm run db:seed", "postinstall": "npx prisma generate"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@hookform/resolvers": "^5.2.1", "@prisma/client": "^6.14.0", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "@types/bcryptjs": "^2.4.6", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.539.0", "next": "15.4.6", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "prisma": "^6.14.0", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "zod": "^4.0.17"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.6", "tailwindcss": "^4", "tw-animate-css": "^1.3.6", "typescript": "^5"}}