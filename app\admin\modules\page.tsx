"use client";

import { useState, useEffect, useCallback } from "react";
import { ErrorBoundary } from "@/components/ErrorBoundary";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ModuleForm } from "@/components/ModuleForm";
import { ConfirmDialog } from "@/components/ConfirmDialog";
import { TableSkeleton, PaginationSkeleton } from "@/components/TableSkeleton";
import { toast } from "sonner";
import { Trash2, Edit, Plus, Filter } from "lucide-react";
import { useClientOnly } from "@/hooks/useClientOnly";
import { isPlaceholderValue } from "@/lib/select-utils";

interface Module {
  Id: number;
  Code: string;
  Name: string;
  project_id: number;
  project: {
    Id: number;
    Code: string;
    ProjectName: string;
  };
}

interface Project {
  Id: number;
  Code: string;
  ProjectName: string;
}

interface ModuleResponse {
  data: Module[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

function AdminModulesPageContent() {
  const mounted = useClientOnly();
  const [modules, setModules] = useState<Module[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(false);
  const [projectsLoading, setProjectsLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [sortBy, setSortBy] = useState("Id");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const [search, setSearch] = useState("");
  const [selectedProjectId, setSelectedProjectId] = useState<string>("all");
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  });
  const [showForm, setShowForm] = useState(false);
  const [editingModule, setEditingModule] = useState<Module | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [deletingModule, setDeletingModule] = useState<Module | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);

  const fetchModules = useCallback(async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        sortBy,
        sortOrder,
        search,
      });

      if (selectedProjectId && selectedProjectId !== "all") {
        params.append("projectId", selectedProjectId);
      }

      const response = await fetch(`/api/admin/modules?${params}`);
      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }
      
      const data: ModuleResponse = await response.json();
      
      // Defensive coding: ensure data.data is an array
      if (data.data && Array.isArray(data.data)) {
        setModules(data.data);
      } else {
        console.error("Invalid modules data structure:", data);
        setModules([]);
      }
      
      // Defensive coding: ensure pagination exists
      if (data.pagination) {
        setPagination(data.pagination);
      }
    } catch (error) {
      console.error("Failed to fetch modules:", error);
      toast.error("Failed to fetch modules");
    } finally {
      setLoading(false);
    }
  }, [page, limit, sortBy, sortOrder, search, selectedProjectId]);

  const fetchProjects = async () => {
    setProjectsLoading(true);
    try {
      const response = await fetch("/api/admin/projects?limit=100"); // Get all projects
      if (response.ok) {
        const data = await response.json();
        // Handle paginated response structure
        if (data.data && Array.isArray(data.data)) {
          setProjects(data.data);
        } else if (Array.isArray(data)) {
          // Fallback for direct array response
          setProjects(data);
        } else {
          console.error("Unexpected API response format:", data);
          setProjects([]);
        }
      } else {
        console.error("Failed to fetch projects:", response.status);
        setProjects([]);
      }
    } catch (error) {
      console.error("Failed to fetch projects:", error);
      setProjects([]);
    } finally {
      setProjectsLoading(false);
    }
  };

  useEffect(() => {
    if (mounted) {
      fetchModules();
    }
  }, [mounted, fetchModules]);

  useEffect(() => {
    if (mounted) {
      fetchProjects();
    }
  }, [mounted]);

  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(column);
      setSortOrder("asc");
    }
  };

  const handleSearch = (value: string) => {
    setSearch(value);
    setPage(1);
  };

  const handleProjectFilter = (projectId: string) => {
    // Ignore disabled placeholder values
    if (isPlaceholderValue(projectId)) {
      return;
    }
    setSelectedProjectId(projectId);
    setPage(1);
  };

  const handlePageSizeChange = (value: string) => {
    setLimit(parseInt(value));
    setPage(1);
  };

  const getSortIcon = (column: string) => {
    if (sortBy !== column) return null;
    return sortOrder === "asc" ? "↑" : "↓";
  };

  const handleAddNew = () => {
    setEditingModule(null);
    setShowForm(true);
  };

  const handleEdit = (module: Module) => {
    setEditingModule(module);
    setShowForm(true);
  };

  const handleDeleteClick = (module: Module) => {
    setDeletingModule(module);
    setShowDeleteDialog(true);
  };

  const handleDeleteConfirm = async () => {
    if (!deletingModule) return;
    
    setDeleteLoading(true);
    try {
      const response = await fetch(`/api/admin/modules/${deletingModule.Id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to delete module");
      }

      await fetchModules();
      setShowDeleteDialog(false);
      setDeletingModule(null);
      toast.success("Module deleted successfully!");
    } catch (error) {
      console.error("Failed to delete module:", error);
      toast.error(
        error instanceof Error 
          ? error.message 
          : "Failed to delete module. Please try again."
      );
    } finally {
      setDeleteLoading(false);
    }
  };

  const handleFormSuccess = () => {
    fetchModules();
    setShowForm(false);
    setEditingModule(null);
  };

  if (!mounted) {
    return (
      <div className="container mx-auto p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <h1 className="text-2xl sm:text-3xl font-bold">Module Management</h1>
          <Button disabled className="w-full sm:w-auto">
            <Plus className="h-4 w-4 mr-2" />
            Add New Module
          </Button>
        </div>
        <div className="flex flex-col gap-4 mb-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Input placeholder="Search by code or name..." disabled />
            </div>
            <div className="flex gap-2">
              <Select disabled>
                <SelectTrigger className="w-full sm:w-48">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="All Projects" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Projects</SelectItem>
                </SelectContent>
              </Select>
              <Select disabled>
                <SelectTrigger className="w-full sm:w-40">
                  <SelectValue placeholder="10 per page" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10 per page</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
        <TableSkeleton rows={limit} columns={5} />
        <PaginationSkeleton />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 sm:p-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <h1 className="text-2xl sm:text-3xl font-bold">Module Management</h1>
        <Button onClick={handleAddNew} className="w-full sm:w-auto">
          <Plus className="h-4 w-4 mr-2" />
          Add New Module
        </Button>
      </div>

      <div className="flex flex-col gap-4 mb-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <Input
              placeholder="Search by code or name..."
              value={search}
              onChange={(e) => handleSearch(e.target.value)}
            />
          </div>
          <div className="flex gap-2">
            <Select 
              value={selectedProjectId} 
              onValueChange={handleProjectFilter}
              disabled={projectsLoading}
            >
              <SelectTrigger className="w-full sm:w-48">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder={projectsLoading ? "Loading..." : "All Projects"} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Projects</SelectItem>
                {projectsLoading ? (
                  <SelectItem value="loading" disabled>
                    Loading projects...
                  </SelectItem>
                ) : Array.isArray(projects) && projects.length > 0 ? (
                  projects.map((project) => (
                    <SelectItem key={project.Id} value={project.Id.toString()}>
                      {project.ProjectName}
                    </SelectItem>
                  ))
                ) : (
                  <SelectItem value="no-projects" disabled>
                    No projects available
                  </SelectItem>
                )}
              </SelectContent>
            </Select>
            <Select value={limit.toString()} onValueChange={handlePageSizeChange}>
              <SelectTrigger className="w-full sm:w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="5">5 per page</SelectItem>
                <SelectItem value="10">10 per page</SelectItem>
                <SelectItem value="25">25 per page</SelectItem>
                <SelectItem value="50">50 per page</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {!mounted || loading ? (
        <TableSkeleton rows={limit} columns={5} />
      ) : (
        <div className="border rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead 
                    className="cursor-pointer hover:bg-gray-50 w-16"
                    onClick={() => handleSort("Id")}
                  >
                    ID {getSortIcon("Id")}
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort("Code")}
                  >
                    Code {getSortIcon("Code")}
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort("Name")}
                  >
                    Name {getSortIcon("Name")}
                  </TableHead>
                  <TableHead>Project</TableHead>
                  <TableHead className="w-32">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {modules.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-4">
                      No modules found
                    </TableCell>
                  </TableRow>
                ) : (
                  modules.map((module) => (
                    <TableRow key={module.Id}>
                      <TableCell>{module.Id}</TableCell>
                      <TableCell>{module.Code}</TableCell>
                      <TableCell>{module.Name}</TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span className="font-medium">{module.project.ProjectName}</span>
                          <span className="text-sm text-gray-500">({module.project.Code})</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleEdit(module)}
                            className="h-8 w-8 p-0"
                          >
                            <Edit className="h-3 w-3" />
                          </Button>
                          <Button 
                            variant="destructive" 
                            size="sm"
                            onClick={() => handleDeleteClick(module)}
                            className="h-8 w-8 p-0"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </div>
      )}

      {!mounted || loading ? (
        <PaginationSkeleton />
      ) : (
        pagination.totalPages > 1 && (
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mt-6">
            <div className="text-sm text-gray-600 order-2 sm:order-1">
              Showing {(pagination.page - 1) * pagination.limit + 1} to{" "}
              {Math.min(pagination.page * pagination.limit, pagination.total)} of{" "}
              {pagination.total} results
            </div>
            <div className="flex gap-2 order-1 sm:order-2">
              <Button
                variant="outline"
                disabled={pagination.page === 1}
                onClick={() => setPage(page - 1)}
                size="sm"
              >
                Previous
              </Button>
              
              <div className="hidden sm:flex gap-1">
                {Array.from({ length: pagination.totalPages }, (_, i) => i + 1)
                  .filter((pageNum) => {
                    if (pagination.totalPages <= 7) return true;
                    if (pageNum === 1 || pageNum === pagination.totalPages) return true;
                    if (Math.abs(pageNum - pagination.page) <= 1) return true;
                    return false;
                  })
                  .map((pageNum, index, array) => {
                    const prevPage = array[index - 1];
                    const showEllipsis = prevPage && pageNum - prevPage > 1;
                    
                    return (
                      <div key={pageNum} className="flex items-center">
                        {showEllipsis && <span className="px-2">...</span>}
                        <Button
                          variant={pageNum === pagination.page ? "default" : "outline"}
                          size="sm"
                          onClick={() => setPage(pageNum)}
                        >
                          {pageNum}
                        </Button>
                      </div>
                    );
                  })}
              </div>

              <div className="sm:hidden flex items-center gap-2 text-sm">
                Page {pagination.page} of {pagination.totalPages}
              </div>

              <Button
                variant="outline"
                disabled={pagination.page === pagination.totalPages}
                onClick={() => setPage(page + 1)}
                size="sm"
              >
                Next
              </Button>
            </div>
          </div>
        )
      )}

      <ModuleForm
        open={showForm}
        onOpenChange={setShowForm}
        module={editingModule}
        onSuccess={handleFormSuccess}
      />

      <ConfirmDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        title="Delete Module"
        description={`Are you sure you want to delete the module "${deletingModule?.Name}"? This action cannot be undone and will fail if the module has related Q&As.`}
        confirmText="Delete"
        cancelText="Cancel"
        variant="destructive"
        onConfirm={handleDeleteConfirm}
        loading={deleteLoading}
      />
    </div>
  );
}

export default function AdminModulesPage() {
  return (
    <ErrorBoundary>
      <AdminModulesPageContent />
    </ErrorBoundary>
  );
}