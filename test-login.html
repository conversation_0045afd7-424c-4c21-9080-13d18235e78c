<!DOCTYPE html>
<html>
<head>
    <title>Test Login</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h2>Test Login Flow</h2>
    
    <div id="register-section">
        <h3>Register User</h3>
        <input type="text" id="reg-username" placeholder="Username" value="testuser2">
        <input type="email" id="reg-email" placeholder="Email" value="<EMAIL>">
        <input type="text" id="reg-fullname" placeholder="Full Name" value="Test User 2">
        <input type="password" id="reg-password" placeholder="Password" value="password123">
        <button onclick="registerUser()">Register</button>
        <div id="register-result"></div>
    </div>

    <hr>

    <div id="login-section">
        <h3>Test Credentials Login</h3>
        <input type="text" id="username" placeholder="Username" value="testuser">
        <input type="password" id="password" placeholder="Password" value="testpass123">
        <button onclick="testLogin()">Login</button>
        <div id="login-result"></div>
    </div>

    <script>
        async function registerUser() {
            const data = {
                username: document.getElementById('reg-username').value,
                email: document.getElementById('reg-email').value,
                fullName: document.getElementById('reg-fullname').value,
                password: document.getElementById('reg-password').value
            };

            try {
                const response = await fetch('/api/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data),
                });

                const result = await response.json();
                document.getElementById('register-result').innerHTML = 
                    '<pre>' + JSON.stringify(result, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('register-result').innerHTML = 
                    '<p style="color: red;">Error: ' + error.message + '</p>';
            }
        }

        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            try {
                // First get CSRF token
                const csrfResponse = await fetch('/api/auth/csrf');
                const { csrfToken } = await csrfResponse.json();

                // Then try to sign in
                const response = await fetch('/api/auth/signin/credentials', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        username: username,
                        password: password,
                        csrfToken: csrfToken,
                        callbackUrl: window.location.origin,
                        json: 'true'
                    }),
                });

                const result = await response.json();
                document.getElementById('login-result').innerHTML = 
                    '<pre>' + JSON.stringify(result, null, 2) + '</pre>';
                
                if (result.url) {
                    document.getElementById('login-result').innerHTML += 
                        '<p style="color: green;">Login successful! Redirect URL: ' + result.url + '</p>';
                }
            } catch (error) {
                document.getElementById('login-result').innerHTML = 
                    '<p style="color: red;">Error: ' + error.message + '</p>';
            }
        }
    </script>
</body>
</html>