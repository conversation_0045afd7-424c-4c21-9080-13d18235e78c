"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";
import { cn } from "@/lib/utils";
import { UserProfile } from "@/components/UserProfile";
import { Menu, X, ChevronDown } from "lucide-react";

const navGroups = [
  {
    title: "Management",
    items: [
      { href: "/admin/status", label: "Statuses" },
      { href: "/admin/modules", label: "Modules" },
    ]
  },
  {
    title: "System",
    items: [
      { href: "/admin/users", label: "Users" },
      { href: "/admin/projects", label: "Projects" },
    ]
  }
];

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [openDropdowns, setOpenDropdowns] = useState<string[]>([]);

  const toggleDropdown = (title: string) => {
    setOpenDropdowns(prev => 
      prev.includes(title) 
        ? prev.filter(item => item !== title)
        : [...prev, title]
    );
  };

  const isActive = (href: string) => pathname === href;
  const hasActiveItem = (items: typeof navGroups[0]['items']) => 
    items.some(item => isActive(item.href));

  return (
    <div className="min-h-screen bg-gray-50">
      <nav className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-8">
              <h1 className="text-xl font-semibold">Smart AIO Admin</h1>
              
              {/* Desktop navigation */}
              <div className="hidden md:flex space-x-6">
                {navGroups.map((group) => (
                  <div key={group.title} className="relative group">
                    <button
                      className={cn(
                        "flex items-center space-x-1 text-sm font-medium transition-colors hover:text-green-600",
                        hasActiveItem(group.items) ? "text-green-600" : "text-gray-600"
                      )}
                    >
                      <span>{group.title}</span>
                      <ChevronDown className="h-4 w-4" />
                    </button>
                    <div className="absolute top-full left-0 mt-1 w-48 bg-white rounded-md shadow-lg border opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                      <div className="py-1">
                        {group.items.map((item) => (
                          <Link
                            key={item.href}
                            href={item.href}
                            className={cn(
                              "block px-4 py-2 text-sm transition-colors hover:bg-gray-50 hover:text-green-600",
                              isActive(item.href) 
                                ? "text-green-600 bg-green-50" 
                                : "text-gray-700"
                            )}
                          >
                            {item.label}
                          </Link>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <UserProfile />
              
              {/* Mobile menu button */}
              <button
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="md:hidden p-2 rounded-md text-gray-600 hover:text-green-600 hover:bg-gray-100"
              >
                {isMobileMenuOpen ? (
                  <X className="h-6 w-6" />
                ) : (
                  <Menu className="h-6 w-6" />
                )}
              </button>
            </div>
          </div>
          
          {/* Mobile navigation */}
          {isMobileMenuOpen && (
            <div className="md:hidden mt-4 pt-4 border-t border-gray-200">
              <div className="space-y-3">
                {navGroups.map((group) => (
                  <div key={group.title}>
                    <button
                      onClick={() => toggleDropdown(group.title)}
                      className={cn(
                        "flex items-center justify-between w-full px-3 py-2 text-left text-sm font-medium rounded-md transition-colors",
                        hasActiveItem(group.items) 
                          ? "text-green-600 bg-green-50" 
                          : "text-gray-700 hover:bg-gray-50"
                      )}
                    >
                      <span>{group.title}</span>
                      <ChevronDown 
                        className={cn(
                          "h-4 w-4 transition-transform",
                          openDropdowns.includes(group.title) ? "rotate-180" : ""
                        )} 
                      />
                    </button>
                    {openDropdowns.includes(group.title) && (
                      <div className="ml-4 mt-2 space-y-1">
                        {group.items.map((item) => (
                          <Link
                            key={item.href}
                            href={item.href}
                            onClick={() => setIsMobileMenuOpen(false)}
                            className={cn(
                              "block px-3 py-2 text-sm rounded-md transition-colors",
                              isActive(item.href)
                                ? "text-green-600 bg-green-50"
                                : "text-gray-600 hover:text-green-600 hover:bg-gray-50"
                            )}
                          >
                            {item.label}
                          </Link>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </nav>
      <main>{children}</main>
    </div>
  );
}