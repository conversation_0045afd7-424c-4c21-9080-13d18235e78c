"use client";

import { useState, useEffect, useCallback } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { StatusForm } from "@/components/StatusForm";
import { ConfirmDialog } from "@/components/ConfirmDialog";
import { TableSkeleton, PaginationSkeleton } from "@/components/TableSkeleton";
import { toast } from "sonner";
import { Trash2, Edit, Plus } from "lucide-react";
import { useClientOnly } from "@/hooks/useClientOnly";

interface Status {
  Id: number;
  Code: string;
  StatusName: string;
}

interface StatusResponse {
  data: Status[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export default function AdminStatusPage() {
  const mounted = useClientOnly();
  const [statuses, setStatuses] = useState<Status[]>([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [sortBy, setSortBy] = useState("Id");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const [search, setSearch] = useState("");
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  });
  const [showForm, setShowForm] = useState(false);
  const [editingStatus, setEditingStatus] = useState<Status | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [deletingStatus, setDeletingStatus] = useState<Status | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);

  const fetchStatuses = useCallback(async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        sortBy,
        sortOrder,
        search,
      });

      const response = await fetch(`/api/admin/status?${params}`);
      const data: StatusResponse = await response.json();
      
      setStatuses(data.data);
      setPagination(data.pagination);
    } catch (error) {
      console.error("Failed to fetch statuses:", error);
    } finally {
      setLoading(false);
    }
  }, [page, limit, sortBy, sortOrder, search]);

  useEffect(() => {
    if (mounted) {
      fetchStatuses();
    }
  }, [mounted, fetchStatuses]);

  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(column);
      setSortOrder("asc");
    }
  };

  const handleSearch = (value: string) => {
    setSearch(value);
    setPage(1);
  };

  const handlePageSizeChange = (value: string) => {
    setLimit(parseInt(value));
    setPage(1);
  };

  const getSortIcon = (column: string) => {
    if (sortBy !== column) return null;
    return sortOrder === "asc" ? "↑" : "↓";
  };

  const handleAddNew = () => {
    setEditingStatus(null);
    setShowForm(true);
  };

  const handleEdit = (status: Status) => {
    setEditingStatus(status);
    setShowForm(true);
  };

  const handleDeleteClick = (status: Status) => {
    setDeletingStatus(status);
    setShowDeleteDialog(true);
  };

  const handleDeleteConfirm = async () => {
    if (!deletingStatus) return;
    
    setDeleteLoading(true);
    try {
      const response = await fetch(`/api/admin/status/${deletingStatus.Id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete status");
      }

      await fetchStatuses();
      setShowDeleteDialog(false);
      setDeletingStatus(null);
      toast.success("Status deleted successfully!");
    } catch (error) {
      console.error("Failed to delete status:", error);
      toast.error("Failed to delete status. Please try again.");
    } finally {
      setDeleteLoading(false);
    }
  };

  const handleFormSuccess = () => {
    fetchStatuses();
    setShowForm(false);
    setEditingStatus(null);
  };

  if (!mounted) {
    return (
      <div className="container mx-auto p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <h1 className="text-2xl sm:text-3xl font-bold">Status Management</h1>
          <Button disabled className="w-full sm:w-auto">
            <Plus className="h-4 w-4 mr-2" />
            Add New Status
          </Button>
        </div>
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex-1">
            <Input placeholder="Search by code or name..." disabled />
          </div>
          <Select disabled>
            <SelectTrigger className="w-full sm:w-40">
              <SelectValue placeholder="10 per page" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10 per page</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <TableSkeleton rows={limit} />
        <PaginationSkeleton />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 sm:p-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <h1 className="text-2xl sm:text-3xl font-bold">Status Management</h1>
        <Button onClick={handleAddNew} className="w-full sm:w-auto">
          <Plus className="h-4 w-4 mr-2" />
          Add New Status
        </Button>
      </div>

      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="flex-1">
          <Input
            placeholder="Search by code or name..."
            value={search}
            onChange={(e) => handleSearch(e.target.value)}
          />
        </div>
        <Select value={limit.toString()} onValueChange={handlePageSizeChange}>
          <SelectTrigger className="w-full sm:w-40">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="5">5 per page</SelectItem>
            <SelectItem value="10">10 per page</SelectItem>
            <SelectItem value="25">25 per page</SelectItem>
            <SelectItem value="50">50 per page</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="border rounded-lg overflow-hidden" suppressHydrationWarning>
        {!mounted || loading ? (
          <TableSkeleton rows={limit} />
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead 
                    className="cursor-pointer hover:bg-gray-50 w-16"
                    onClick={() => handleSort("Id")}
                  >
                    ID {getSortIcon("Id")}
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort("Code")}
                  >
                    Code {getSortIcon("Code")}
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort("StatusName")}
                  >
                    Status Name {getSortIcon("StatusName")}
                  </TableHead>
                  <TableHead className="w-32">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {statuses.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={4} className="text-center py-4">
                      No statuses found
                    </TableCell>
                  </TableRow>
                ) : (
                  statuses.map((status) => (
                    <TableRow key={status.Id}>
                      <TableCell>{status.Id}</TableCell>
                      <TableCell>{status.Code}</TableCell>
                      <TableCell>{status.StatusName}</TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleEdit(status)}
                            className="h-8 w-8 p-0"
                          >
                            <Edit className="h-3 w-3" />
                          </Button>
                          <Button 
                            variant="destructive" 
                            size="sm"
                            onClick={() => handleDeleteClick(status)}
                            className="h-8 w-8 p-0"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        )}
      </div>

      {!mounted || loading ? (
        <PaginationSkeleton />
      ) : (
        pagination.totalPages > 1 && (
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mt-6">
            <div className="text-sm text-gray-600 order-2 sm:order-1">
              Showing {(pagination.page - 1) * pagination.limit + 1} to{" "}
              {Math.min(pagination.page * pagination.limit, pagination.total)} of{" "}
              {pagination.total} results
            </div>
            <div className="flex gap-2 order-1 sm:order-2">
              <Button
                variant="outline"
                disabled={pagination.page === 1}
                onClick={() => setPage(page - 1)}
                size="sm"
              >
                Previous
              </Button>
              
              <div className="hidden sm:flex gap-1">
                {Array.from({ length: pagination.totalPages }, (_, i) => i + 1)
                  .filter((pageNum) => {
                    if (pagination.totalPages <= 7) return true;
                    if (pageNum === 1 || pageNum === pagination.totalPages) return true;
                    if (Math.abs(pageNum - pagination.page) <= 1) return true;
                    return false;
                  })
                  .map((pageNum, index, array) => {
                    const prevPage = array[index - 1];
                    const showEllipsis = prevPage && pageNum - prevPage > 1;
                    
                    return (
                      <div key={pageNum} className="flex items-center">
                        {showEllipsis && <span className="px-2">...</span>}
                        <Button
                          variant={pageNum === pagination.page ? "default" : "outline"}
                          size="sm"
                          onClick={() => setPage(pageNum)}
                        >
                          {pageNum}
                        </Button>
                      </div>
                    );
                  })}
              </div>

              <div className="sm:hidden flex items-center gap-2 text-sm">
                Page {pagination.page} of {pagination.totalPages}
              </div>

              <Button
                variant="outline"
                disabled={pagination.page === pagination.totalPages}
                onClick={() => setPage(page + 1)}
                size="sm"
              >
                Next
              </Button>
            </div>
          </div>
        )
      )}

      <StatusForm
        open={showForm}
        onOpenChange={setShowForm}
        status={editingStatus}
        onSuccess={handleFormSuccess}
      />

      <ConfirmDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        title="Delete Status"
        description={`Are you sure you want to delete the status "${deletingStatus?.StatusName}"? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        variant="destructive"
        onConfirm={handleDeleteConfirm}
        loading={deleteLoading}
      />
    </div>
  );
}