import { NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export async function GET() {
  try {
    const [projects, statuses, modules] = await Promise.all([
      prisma.project.findMany({
        select: {
          Id: true,
          ProjectName: true,
        },
      }),
      prisma.status.findMany({
        select: {
          Id: true,
          StatusName: true,
        },
      }),
      prisma.module.findMany({
        select: {
          Id: true,
          Name: true,
        },
      }),
    ]);

    return NextResponse.json({
      projects,
      statuses,
      modules,
    });
  } catch (error) {
    console.error("Failed to fetch dropdown data:", error);
    return NextResponse.json(
      { error: "Failed to fetch dropdown data" },
      { status: 500 }
    );
  }
}