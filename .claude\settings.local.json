{"permissions": {"allow": ["Bash(npm install:*)", "Bash(npx prisma:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(taskkill:*)", "Bash(npx shadcn@latest add:*)", "Bash(node:*)", "<PERSON><PERSON>(curl:*)", "Bash(npm run lint)", "Bash(npx tsc:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npm run lint:*)", "Bash(npm run build:*)", "Bash(timeout 30 npx prisma generate --force)", "Bash(rm:*)", "Bash(PORT=3020 npm run dev)", "Bash(PORT=3030 npm run dev)", "Bash(PORT=3040 npm run dev)", "Bash(PORT=4000 npm run dev)"], "deny": [], "ask": [], "defaultMode": "acceptEdits"}}