"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { toast } from "sonner";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

const statusSchema = z.object({
  Code: z.string().min(1, "Code is required").max(50, "Code must be less than 50 characters"),
  StatusName: z.string().min(1, "Status name is required").max(100, "Status name must be less than 100 characters"),
});

type StatusFormData = z.infer<typeof statusSchema>;

interface Status {
  Id: number;
  Code: string;
  StatusName: string;
}

interface StatusFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  status?: Status | null;
  onSuccess: () => void;
}

export function StatusForm({ open, onOpenChange, status, onSuccess }: StatusFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const isEditing = !!status;

  const form = useForm<StatusFormData>({
    resolver: zodResolver(statusSchema),
    defaultValues: {
      Code: "",
      StatusName: "",
    },
  });

  // Reset form when status changes or dialog opens
  React.useEffect(() => {
    if (open) {
      form.reset({
        Code: status?.Code || "",
        StatusName: status?.StatusName || "",
      });
    }
  }, [open, status, form]);

  const onSubmit = async (data: StatusFormData) => {
    setIsLoading(true);
    try {
      const url = isEditing 
        ? `/api/admin/status/${status.Id}`
        : `/api/admin/status`;
      
      const method = isEditing ? "PUT" : "POST";
      
      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to save status");
      }

      form.reset();
      onOpenChange(false);
      onSuccess();
      
      toast.success(
        isEditing 
          ? "Status updated successfully!" 
          : "Status created successfully!"
      );
    } catch (error) {
      console.error("Error saving status:", error);
      toast.error(
        error instanceof Error 
          ? error.message 
          : "Failed to save status. Please try again."
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      form.reset();
    }
    onOpenChange(newOpen);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? "Edit Status" : "Add New Status"}
          </DialogTitle>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="Code"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Code</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="Enter status code" 
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="StatusName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Status Name</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="Enter status name" 
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <div className="flex justify-end gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => handleOpenChange(false)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? "Saving..." : isEditing ? "Update" : "Create"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}