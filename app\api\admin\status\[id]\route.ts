import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: idParam } = await params;
    const id = parseInt(idParam);
    
    if (isNaN(id)) {
      return NextResponse.json(
        { error: "Invalid ID" },
        { status: 400 }
      );
    }

    const status = await prisma.status.findUnique({
      where: { Id: id },
    });

    if (!status) {
      return NextResponse.json(
        { error: "Status not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(status);
  } catch {
    return NextResponse.json(
      { error: "Failed to fetch status" },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: idParam } = await params;
    const id = parseInt(idParam);
    const { Code, StatusName } = await request.json();

    if (isNaN(id)) {
      return NextResponse.json(
        { error: "Invalid ID" },
        { status: 400 }
      );
    }

    if (!Code || !StatusName) {
      return NextResponse.json(
        { error: "Code and StatusName are required" },
        { status: 400 }
      );
    }

    const status = await prisma.status.update({
      where: { Id: id },
      data: {
        Code,
        StatusName,
      },
    });

    return NextResponse.json(status);
  } catch {
    return NextResponse.json(
      { error: "Failed to update status" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: idParam } = await params;
    const id = parseInt(idParam);
    
    if (isNaN(id)) {
      return NextResponse.json(
        { error: "Invalid ID" },
        { status: 400 }
      );
    }

    await prisma.status.delete({
      where: { Id: id },
    });

    return NextResponse.json({ message: "Status deleted successfully" });
  } catch {
    return NextResponse.json(
      { error: "Failed to delete status" },
      { status: 500 }
    );
  }
}