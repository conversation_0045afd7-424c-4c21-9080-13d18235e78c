"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import { Upload, X, Image } from "lucide-react";

const formSchema = z.object({
  Question: z.string().min(1, "Question is required"),
  QuestionBy: z.string().min(1, "Question by is required"),
  Answer: z.string().optional(),
  AnswerBy: z.string().optional(),
  project_id: z.number().min(1, "Project is required"),
  status_id: z.number().min(1, "Status is required"),
  module_id: z.number().min(1, "Module is required"),
});

type FormValues = z.infer<typeof formSchema>;

interface QA {
  Id: number;
  ModuleName: string;
  Question: string;
  QuestionBy: string;
  QuestionDateTime: string;
  Answer: string | null;
  AnswerBy: string | null;
  AnswerDateTime: string | null;
  ImageUrl: string | null;
  QuestionImages: string[];
  AnswerImages: string[];
  project_id: number;
  status_id: number;
  module_id: number;
  project: {
    Id: number;
    ProjectName: string;
  };
  status: {
    Id: number;
    StatusName: string;
  };
  module: {
    Id: number;
    Name: string;
  };
}

interface QAFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  qa?: QA | null;
  onSuccess: () => void;
}

interface Project {
  Id: number;
  ProjectName: string;
}

interface Status {
  Id: number;
  StatusName: string;
}

interface Module {
  Id: number;
  Name: string;
}

export function QAForm({ open, onOpenChange, qa, onSuccess }: QAFormProps) {
  const [loading, setLoading] = useState(false);
  const [dataLoading, setDataLoading] = useState(false);
  const [projects, setProjects] = useState<Project[]>([]);
  const [statuses, setStatuses] = useState<Status[]>([]);
  const [modules, setModules] = useState<Module[]>([]);
  const [questionFiles, setQuestionFiles] = useState<File[]>([]);
  const [answerFiles, setAnswerFiles] = useState<File[]>([]);
  const [existingQuestionImages, setExistingQuestionImages] = useState<string[]>([]);
  const [existingAnswerImages, setExistingAnswerImages] = useState<string[]>([]);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      Question: "",
      QuestionBy: "",
      Answer: "",
      AnswerBy: "",
      project_id: 0,
      status_id: 0,
      module_id: 0,
    },
  });

  useEffect(() => {
    if (open) {
      fetchDropdownData();
      if (qa) {
        form.reset({
          Question: qa.Question,
          QuestionBy: qa.QuestionBy,
          Answer: qa.Answer || "",
          AnswerBy: qa.AnswerBy || "",
          project_id: qa.project_id,
          status_id: qa.status_id,
          module_id: qa.module_id,
        });
        setExistingQuestionImages(qa.QuestionImages || []);
        setExistingAnswerImages(qa.AnswerImages || []);
      } else {
        form.reset({
          Question: "",
          QuestionBy: "",
          Answer: "",
          AnswerBy: "",
          project_id: 0,
          status_id: 0,
          module_id: 0,
        });
        setExistingQuestionImages([]);
        setExistingAnswerImages([]);
      }
      setQuestionFiles([]);
      setAnswerFiles([]);
    }
  }, [open, qa, form]);

  const fetchDropdownData = async () => {
    setDataLoading(true);
    try {
      const response = await fetch("/api/qa/dropdown-data");
      if (!response.ok) {
        throw new Error("Failed to fetch dropdown data");
      }
      const data = await response.json();
      setProjects(data.projects);
      setStatuses(data.statuses);
      setModules(data.modules);
    } catch (error) {
      console.error("Failed to fetch dropdown data:", error);
      toast.error("Failed to load form data");
    } finally {
      setDataLoading(false);
    }
  };

  const handleQuestionFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    const validFiles = files.filter(file => {
      if (file.size > 5 * 1024 * 1024) {
        toast.error(`File ${file.name} is too large. Maximum size is 5MB.`);
        return false;
      }
      return true;
    });
    setQuestionFiles(prev => [...prev, ...validFiles]);
  };

  const handleAnswerFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    const validFiles = files.filter(file => {
      if (file.size > 5 * 1024 * 1024) {
        toast.error(`File ${file.name} is too large. Maximum size is 5MB.`);
        return false;
      }
      return true;
    });
    setAnswerFiles(prev => [...prev, ...validFiles]);
  };

  const removeQuestionFile = (index: number) => {
    setQuestionFiles(prev => prev.filter((_, i) => i !== index));
  };

  const removeAnswerFile = (index: number) => {
    setAnswerFiles(prev => prev.filter((_, i) => i !== index));
  };

  const removeExistingQuestionImage = (index: number) => {
    setExistingQuestionImages(prev => prev.filter((_, i) => i !== index));
  };

  const removeExistingAnswerImage = (index: number) => {
    setExistingAnswerImages(prev => prev.filter((_, i) => i !== index));
  };

  const onSubmit = async (values: FormValues) => {
    setLoading(true);
    try {
      const formData = new FormData();
      
      // Add form fields
      Object.entries(values).forEach(([key, value]) => {
        if (value !== undefined && value !== "") {
          formData.append(key, value.toString());
        }
      });

      // Add existing images
      formData.append("existingQuestionImages", JSON.stringify(existingQuestionImages));
      formData.append("existingAnswerImages", JSON.stringify(existingAnswerImages));

      // Add new files
      questionFiles.forEach(file => {
        formData.append("questionImages", file);
      });
      answerFiles.forEach(file => {
        formData.append("answerImages", file);
      });

      const url = qa ? `/api/qa/${qa.Id}` : "/api/qa";
      const method = qa ? "PUT" : "POST";

      const response = await fetch(url, {
        method,
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to save QA");
      }

      toast.success(qa ? "QA updated successfully!" : "QA created successfully!");
      onSuccess();
    } catch (error) {
      console.error("Failed to save QA:", error);
      toast.error(error instanceof Error ? error.message : "Failed to save QA");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {qa ? "Edit Q&A" : "Add New Q&A"}
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="project_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Project</FormLabel>
                    <Select 
                      onValueChange={(value) => field.onChange(parseInt(value))}
                      value={field.value > 0 ? field.value.toString() : ""}
                      disabled={dataLoading}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={dataLoading ? "Loading..." : "Select project"} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {projects.map((project) => (
                          <SelectItem key={project.Id} value={project.Id.toString()}>
                            {project.ProjectName}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="module_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Module</FormLabel>
                    <Select 
                      onValueChange={(value) => field.onChange(parseInt(value))}
                      value={field.value > 0 ? field.value.toString() : ""}
                      disabled={dataLoading}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={dataLoading ? "Loading..." : "Select module"} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {modules.map((module) => (
                          <SelectItem key={module.Id} value={module.Id.toString()}>
                            {module.Name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="status_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Status</FormLabel>
                  <Select 
                    onValueChange={(value) => field.onChange(parseInt(value))}
                    value={field.value > 0 ? field.value.toString() : ""}
                    disabled={dataLoading}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder={dataLoading ? "Loading..." : "Select status"} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {statuses.map((status) => (
                        <SelectItem key={status.Id} value={status.Id.toString()}>
                          {status.StatusName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="Question"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Question</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Enter your question..." {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="QuestionBy"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Asked By</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div>
              <FormLabel>Question Images</FormLabel>
              <div className="mt-2">
                <input
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={handleQuestionFileChange}
                  className="hidden"
                  id="question-images"
                />
                <label htmlFor="question-images">
                  <Button type="button" variant="outline" className="cursor-pointer" asChild>
                    <span>
                      <Upload className="h-4 w-4 mr-2" />
                      Upload Images
                    </span>
                  </Button>
                </label>
                
                {/* Existing Images */}
                {existingQuestionImages.length > 0 && (
                  <div className="mt-2">
                    <p className="text-sm font-medium">Existing Images:</p>
                    <div className="flex flex-wrap gap-2 mt-1">
                      {existingQuestionImages.map((url, index) => (
                        <div key={index} className="relative">
                          <div className="flex items-center gap-2 bg-gray-100 p-2 rounded">
                            <Image className="h-4 w-4" />
                            <span className="text-sm truncate max-w-32">
                              {url.split('/').pop()}
                            </span>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeExistingQuestionImage(index)}
                              className="h-4 w-4 p-0"
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* New Files */}
                {questionFiles.length > 0 && (
                  <div className="mt-2">
                    <p className="text-sm font-medium">New Images:</p>
                    <div className="flex flex-wrap gap-2 mt-1">
                      {questionFiles.map((file, index) => (
                        <div key={index} className="flex items-center gap-2 bg-blue-50 p-2 rounded">
                          <Image className="h-4 w-4 text-blue-500" />
                          <span className="text-sm truncate max-w-32">{file.name}</span>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeQuestionFile(index)}
                            className="h-4 w-4 p-0"
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            <FormField
              control={form.control}
              name="Answer"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Answer (Optional)</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Enter answer..." {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="AnswerBy"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Answered By (Optional)</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div>
              <FormLabel>Answer Images</FormLabel>
              <div className="mt-2">
                <input
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={handleAnswerFileChange}
                  className="hidden"
                  id="answer-images"
                />
                <label htmlFor="answer-images">
                  <Button type="button" variant="outline" className="cursor-pointer" asChild>
                    <span>
                      <Upload className="h-4 w-4 mr-2" />
                      Upload Images
                    </span>
                  </Button>
                </label>
                
                {/* Existing Images */}
                {existingAnswerImages.length > 0 && (
                  <div className="mt-2">
                    <p className="text-sm font-medium">Existing Images:</p>
                    <div className="flex flex-wrap gap-2 mt-1">
                      {existingAnswerImages.map((url, index) => (
                        <div key={index} className="relative">
                          <div className="flex items-center gap-2 bg-gray-100 p-2 rounded">
                            <Image className="h-4 w-4" />
                            <span className="text-sm truncate max-w-32">
                              {url.split('/').pop()}
                            </span>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeExistingAnswerImage(index)}
                              className="h-4 w-4 p-0"
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* New Files */}
                {answerFiles.length > 0 && (
                  <div className="mt-2">
                    <p className="text-sm font-medium">New Images:</p>
                    <div className="flex flex-wrap gap-2 mt-1">
                      {answerFiles.map((file, index) => (
                        <div key={index} className="flex items-center gap-2 bg-blue-50 p-2 rounded">
                          <Image className="h-4 w-4 text-blue-500" />
                          <span className="text-sm truncate max-w-32">{file.name}</span>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeAnswerFile(index)}
                            className="h-4 w-4 p-0"
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? "Saving..." : qa ? "Update" : "Create"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}