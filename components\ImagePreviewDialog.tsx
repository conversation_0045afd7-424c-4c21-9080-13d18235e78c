"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Download, ZoomIn, ZoomOut, RotateCw, X, Maximize2 } from "lucide-react";

interface ImagePreviewDialogProps {
  isOpen: boolean;
  onClose: () => void;
  imageUrl: string;
  imageName?: string;
}

export function ImagePreviewDialog({
  isOpen,
  onClose,
  imageUrl,
  imageName,
}: ImagePreviewDialogProps) {
  const [scale, setScale] = useState(1);
  const [rotation, setRotation] = useState(0);
  const [fitToScreen, setFitToScreen] = useState(true);
  const [panOffset, setPanOffset] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

  const handleZoomIn = () => {
    setFitToScreen(false);
    setScale(prev => Math.min(prev + 0.25, 5));
  };

  const handleZoomOut = () => {
    setFitToScreen(false);
    setScale(prev => Math.max(prev - 0.25, 0.1));
  };

  const handleRotate = () => {
    setRotation(prev => (prev + 90) % 360);
  };

  const handleFitToScreen = () => {
    setFitToScreen(true);
    setScale(1);
    setPanOffset({ x: 0, y: 0 });
  };

  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = imageName || 'image.png';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const resetTransforms = () => {
    setScale(1);
    setRotation(0);
    setFitToScreen(true);
    setPanOffset({ x: 0, y: 0 });
  };

  const handleDialogClose = () => {
    resetTransforms();
    onClose();
  };

  // Reset fit to screen when dialog opens
  useEffect(() => {
    if (isOpen) {
      setFitToScreen(true);
      setScale(1);
      setRotation(0);
      setPanOffset({ x: 0, y: 0 });
    }
  }, [isOpen]);

  // Mouse/Touch drag handlers
  const handleMouseDown = (e: React.MouseEvent) => {
    if (!fitToScreen && scale > 1) {
      setIsDragging(true);
      setDragStart({ x: e.clientX - panOffset.x, y: e.clientY - panOffset.y });
      e.preventDefault();
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging && !fitToScreen && scale > 1) {
      setPanOffset({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y,
      });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleTouchStart = (e: React.TouchEvent) => {
    if (!fitToScreen && scale > 1 && e.touches.length === 1) {
      setIsDragging(true);
      const touch = e.touches[0];
      setDragStart({ x: touch.clientX - panOffset.x, y: touch.clientY - panOffset.y });
      e.preventDefault();
    }
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (isDragging && !fitToScreen && scale > 1 && e.touches.length === 1) {
      const touch = e.touches[0];
      setPanOffset({
        x: touch.clientX - dragStart.x,
        y: touch.clientY - dragStart.y,
      });
    }
  };

  const handleTouchEnd = () => {
    setIsDragging(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleDialogClose}>
      <DialogContent className="w-[90vw] h-[99vh] max-w-none max-h-none p-0 overflow-hidden" showCloseButton={false}>
        {/* Fixed Header */}
        <DialogHeader className="flex-shrink-0 p-3 border-b bg-white z-10 relative">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-sm truncate pr-4">
              {imageName || "Image Preview"}
            </DialogTitle>
            <div className="flex items-center gap-1">
              <Button
                variant="outline"
                size="sm"
                onClick={handleZoomOut}
                disabled={scale <= 0.1}
                className="h-7 w-7 p-0"
                title="Zoom Out"
              >
                <ZoomOut className="h-3 w-3" />
              </Button>
              <span className="text-xs text-muted-foreground min-w-[2.5rem] text-center">
                {fitToScreen ? 'Fit' : `${Math.round(scale * 100)}%`}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={handleZoomIn}
                disabled={scale >= 5}
                className="h-7 w-7 p-0"
                title="Zoom In"
              >
                <ZoomIn className="h-3 w-3" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleFitToScreen}
                className="h-7 w-7 p-0"
                title="Fit to Screen"
              >
                <Maximize2 className="h-3 w-3" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRotate}
                className="h-7 w-7 p-0"
                title="Rotate"
              >
                <RotateCw className="h-3 w-3" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleDownload}
                className="h-7 w-7 p-0"
                title="Download"
              >
                <Download className="h-3 w-3" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleDialogClose}
                className="h-7 w-7 p-0"
                title="Close"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </DialogHeader>
        
        {/* Image Container - Takes full remaining space */}
        <div 
          className="flex-1 overflow-hidden bg-gray-900 relative"
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
          style={{ cursor: !fitToScreen && scale > 1 ? (isDragging ? 'grabbing' : 'grab') : 'default' }}
        >
          <div 
            className="w-full h-full flex items-center justify-center p-4"
            style={{ 
              minHeight: 'calc(99vh - 60px)',
              minWidth: fitToScreen ? 'auto' : `${scale * 100}%`,
              transform: !fitToScreen && scale > 1 ? `translate(${panOffset.x}px, ${panOffset.y}px)` : 'none',
              transition: isDragging ? 'none' : 'transform 0.2s ease-in-out',
            }}
          >
            <img
              src={imageUrl}
              alt={imageName || "Preview"}
              className={`transition-transform duration-200 ease-in-out ${
                fitToScreen 
                  ? 'max-w-full max-h-full object-contain' 
                  : 'object-contain'
              }`}
              style={{
                transform: `scale(${fitToScreen ? 1 : scale}) rotate(${rotation}deg)`,
                transformOrigin: 'center',
                width: fitToScreen ? 'auto' : 'auto',
                height: fitToScreen ? 'auto' : 'auto',
                userSelect: 'none',
                pointerEvents: 'none',
              }}
              onError={(e) => {
                console.error('Failed to load image:', imageUrl);
                e.currentTarget.src = 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="400" height="300" viewBox="0 0 400 300"><rect width="400" height="300" fill="%23374151"/><text x="200" y="150" text-anchor="middle" dominant-baseline="middle" font-family="sans-serif" font-size="16" fill="%23f9fafb">Failed to load image</text></svg>';
              }}
              draggable={false}
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}