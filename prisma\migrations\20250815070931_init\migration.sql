-- CreateTable
CREATE TABLE "public"."projects" (
    "Id" SERIAL NOT NULL,
    "Code" TEXT NOT NULL,
    "ProjectName" TEXT NOT NULL,
    "CustomerName" TEXT NOT NULL,
    "StartDate" TIMESTAMP(3) NOT NULL,
    "ExpiredDate" TIMESTAMP(3) NOT NULL,
    "Period" INTEGER NOT NULL,

    CONSTRAINT "projects_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "public"."statuses" (
    "Id" SERIAL NOT NULL,
    "Code" TEXT NOT NULL,
    "StatusName" TEXT NOT NULL,

    CONSTRAINT "statuses_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "public"."modules" (
    "Id" SERIAL NOT NULL,
    "Code" TEXT NOT NULL,
    "Name" TEXT NOT NULL,
    "project_id" INTEGER NOT NULL,

    CONSTRAINT "modules_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "public"."qas" (
    "Id" SERIAL NOT NULL,
    "ModuleName" TEXT NOT NULL,
    "Question" TEXT NOT NULL,
    "QuestionBy" TEXT NOT NULL,
    "QuestionDateTime" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "Answer" TEXT,
    "AnswerBy" TEXT,
    "AnswerDateTime" TIMESTAMP(3),
    "project_id" INTEGER NOT NULL,
    "status_id" INTEGER NOT NULL,
    "module_id" INTEGER NOT NULL,

    CONSTRAINT "qas_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "public"."accounts" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "provider" TEXT NOT NULL,
    "providerAccountId" TEXT NOT NULL,
    "refresh_token" TEXT,
    "access_token" TEXT,
    "expires_at" INTEGER,
    "token_type" TEXT,
    "scope" TEXT,
    "id_token" TEXT,
    "session_state" TEXT,

    CONSTRAINT "accounts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."sessions" (
    "id" TEXT NOT NULL,
    "sessionToken" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "expires" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "sessions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."users" (
    "id" TEXT NOT NULL,
    "name" TEXT,
    "email" TEXT NOT NULL,
    "emailVerified" TIMESTAMP(3),
    "image" TEXT,

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."verification_tokens" (
    "identifier" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "expires" TIMESTAMP(3) NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "projects_Code_key" ON "public"."projects"("Code");

-- CreateIndex
CREATE UNIQUE INDEX "statuses_Code_key" ON "public"."statuses"("Code");

-- CreateIndex
CREATE UNIQUE INDEX "modules_Code_key" ON "public"."modules"("Code");

-- CreateIndex
CREATE UNIQUE INDEX "accounts_provider_providerAccountId_key" ON "public"."accounts"("provider", "providerAccountId");

-- CreateIndex
CREATE UNIQUE INDEX "sessions_sessionToken_key" ON "public"."sessions"("sessionToken");

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "public"."users"("email");

-- CreateIndex
CREATE UNIQUE INDEX "verification_tokens_token_key" ON "public"."verification_tokens"("token");

-- CreateIndex
CREATE UNIQUE INDEX "verification_tokens_identifier_token_key" ON "public"."verification_tokens"("identifier", "token");

-- AddForeignKey
ALTER TABLE "public"."modules" ADD CONSTRAINT "modules_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."projects"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."qas" ADD CONSTRAINT "qas_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "public"."projects"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."qas" ADD CONSTRAINT "qas_status_id_fkey" FOREIGN KEY ("status_id") REFERENCES "public"."statuses"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."qas" ADD CONSTRAINT "qas_module_id_fkey" FOREIGN KEY ("module_id") REFERENCES "public"."modules"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."accounts" ADD CONSTRAINT "accounts_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."sessions" ADD CONSTRAINT "sessions_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
