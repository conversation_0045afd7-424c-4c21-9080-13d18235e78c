"use client"

import { useSession, signOut } from "next-auth/react"
import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";

export default function Home() {
  const { data: session, status } = useSession()
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  if (!isClient) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">Loading...</div>
      </div>
    )
  }

  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">Loading...</div>
      </div>
    )
  }
  return (
    <div className="font-sans grid grid-rows-[20px_1fr_20px] items-center justify-items-center min-h-screen p-8 pb-20 gap-16 sm:p-20">
      <header className="absolute top-4 right-4">
        <div className="flex items-center gap-4">
          {session?.user?.image && (
            <Image
              src={session.user.image}
              alt="Profile"
              width={32}
              height={32}
              className="rounded-full"
            />
          )}
          <span className="text-sm">{session?.user?.name}</span>
          <button
            onClick={() => signOut()}
            className="bg-red-500 text-white px-3 py-1 rounded text-sm hover:bg-red-600"
          >
            Sign Out
          </button>
        </div>
      </header>
      <main className="flex flex-col gap-[32px] row-start-2 items-center sm:items-start">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-4">Smart AIO</h1>
          <p className="text-gray-600">Welcome to your knowledge management system</p>
        </div>
        <div className="flex flex-col sm:flex-row gap-6 items-center">
          <Link
            href="/qa"
            className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors"
          >
            Browse Q&A
          </Link>
          <Link
            href="/admin/status"
            className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Admin Panel
          </Link>
        </div>
        
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">Features</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-left">
            <div className="p-4 border rounded-lg">
              <h3 className="font-semibold mb-2">Q&A System</h3>
              <p className="text-gray-600 text-sm">Ask questions, get answers, and browse existing knowledge.</p>
            </div>
            <div className="p-4 border rounded-lg">
              <h3 className="font-semibold mb-2">Admin Management</h3>
              <p className="text-gray-600 text-sm">Manage statuses, modules, users, and projects.</p>
            </div>
          </div>
        </div>

      </main>
    </div>
  );
}
