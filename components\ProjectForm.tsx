"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { toast } from "sonner";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

const projectSchema = z.object({
  Code: z.string().min(1, "Code is required").max(50, "Code must be less than 50 characters"),
  ProjectName: z.string().min(1, "Project name is required").max(100, "Project name must be less than 100 characters"),
  CustomerName: z.string().min(1, "Customer name is required").max(100, "Customer name must be less than 100 characters"),
  StartDate: z.string().min(1, "Start date is required"),
  ExpiredDate: z.string().min(1, "End date is required"),
  Period: z.number().min(1, "Period must be at least 1 day"),
});

type ProjectFormData = z.infer<typeof projectSchema>;

interface Project {
  Id: number;
  Code: string;
  ProjectName: string;
  CustomerName: string;
  StartDate: string;
  ExpiredDate: string;
  Period: number;
}

interface ProjectFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  project?: Project | null;
  onSuccess: () => void;
}

export function ProjectForm({ open, onOpenChange, project, onSuccess }: ProjectFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const isEditing = !!project;

  const form = useForm<ProjectFormData>({
    resolver: zodResolver(projectSchema),
    defaultValues: {
      Code: "",
      ProjectName: "",
      CustomerName: "",
      StartDate: "",
      ExpiredDate: "",
      Period: 1,
    },
  });

  // Reset form when project changes or dialog opens
  React.useEffect(() => {
    if (open) {
      const startDate = project?.StartDate ? new Date(project.StartDate).toISOString().split('T')[0] : "";
      const expiredDate = project?.ExpiredDate ? new Date(project.ExpiredDate).toISOString().split('T')[0] : "";
      
      form.reset({
        Code: project?.Code || "",
        ProjectName: project?.ProjectName || "",
        CustomerName: project?.CustomerName || "",
        StartDate: startDate,
        ExpiredDate: expiredDate,
        Period: project?.Period || 1,
      });
    }
  }, [open, project, form]);

  const onSubmit = async (data: ProjectFormData) => {
    setIsLoading(true);
    try {
      const url = isEditing 
        ? `/api/admin/projects/${project.Id}`
        : `/api/admin/projects`;
      
      const method = isEditing ? "PUT" : "POST";
      
      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to save project");
      }

      form.reset();
      onOpenChange(false);
      onSuccess();
      
      toast.success(
        isEditing 
          ? "Project updated successfully!" 
          : "Project created successfully!"
      );
    } catch (error) {
      console.error("Error saving project:", error);
      toast.error(
        error instanceof Error 
          ? error.message 
          : "Failed to save project. Please try again."
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      form.reset();
    }
    onOpenChange(newOpen);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? "Edit Project" : "Add New Project"}
          </DialogTitle>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="Code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Code</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="Enter project code" 
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="Period"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Period (days)</FormLabel>
                    <FormControl>
                      <Input 
                        type="number"
                        placeholder="Enter period in days" 
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="ProjectName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Project Name</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="Enter project name" 
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="CustomerName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Customer Name</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="Enter customer name" 
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="StartDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Start Date</FormLabel>
                    <FormControl>
                      <Input 
                        type="date"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="ExpiredDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>End Date</FormLabel>
                    <FormControl>
                      <Input 
                        type="date"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            
            <div className="flex justify-end gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => handleOpenChange(false)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? "Saving..." : isEditing ? "Update" : "Create"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}