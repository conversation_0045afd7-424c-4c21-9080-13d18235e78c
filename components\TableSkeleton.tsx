import { Skeleton } from "@/components/ui/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface TableSkeletonProps {
  rows?: number;
  columns?: number;
}

export function TableSkeleton({ rows = 5, columns = 4 }: TableSkeletonProps) {
  const renderHeaders = () => {
    if (columns === 5) {
      // For modules table (5 columns)
      return (
        <>
          <TableHead className="w-16">
            <Skeleton className="h-4 w-8" />
          </TableHead>
          <TableHead>
            <Skeleton className="h-4 w-16" />
          </TableHead>
          <TableHead>
            <Skeleton className="h-4 w-24" />
          </TableHead>
          <TableHead>
            <Skeleton className="h-4 w-20" />
          </TableHead>
          <TableHead className="w-32">
            <Skeleton className="h-4 w-16" />
          </TableHead>
        </>
      );
    }
    
    // Default for status table (4 columns)
    return (
      <>
        <TableHead className="w-16">
          <Skeleton className="h-4 w-8" />
        </TableHead>
        <TableHead>
          <Skeleton className="h-4 w-16" />
        </TableHead>
        <TableHead>
          <Skeleton className="h-4 w-24" />
        </TableHead>
        <TableHead className="w-32">
          <Skeleton className="h-4 w-16" />
        </TableHead>
      </>
    );
  };

  return (
    <div className="border rounded-lg overflow-hidden">
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              {renderHeaders()}
            </TableRow>
          </TableHeader>
          <TableBody>
            {Array.from({ length: rows }).map((_, rowIndex) => (
              <TableRow key={rowIndex}>
                <TableCell>
                  <Skeleton className="h-4 w-8" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-4 w-20" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-4 w-32" />
                </TableCell>
                {columns === 5 && (
                  <TableCell>
                    <div className="flex flex-col gap-1">
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-3 w-16" />
                    </div>
                  </TableCell>
                )}
                <TableCell>
                  <div className="flex gap-1">
                    <Skeleton className="h-8 w-8 rounded" />
                    <Skeleton className="h-8 w-8 rounded" />
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

export function PaginationSkeleton() {
  return (
    <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mt-6">
      <div className="order-2 sm:order-1">
        <Skeleton className="h-4 w-48" />
      </div>
      <div className="flex gap-2 order-1 sm:order-2">
        <Skeleton className="h-8 w-16" />
        <div className="hidden sm:flex gap-1">
          <Skeleton className="h-8 w-8" />
          <Skeleton className="h-8 w-8" />
          <Skeleton className="h-8 w-8" />
        </div>
        <div className="sm:hidden">
          <Skeleton className="h-4 w-16" />
        </div>
        <Skeleton className="h-8 w-12" />
      </div>
    </div>
  );
}