import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: idParam } = await params;
    const id = parseInt(idParam);
    
    if (isNaN(id)) {
      return NextResponse.json(
        { error: "Invalid ID" },
        { status: 400 }
      );
    }

    const qa = await prisma.qA.findUnique({
      where: { Id: id },
      include: {
        project: true,
        status: true,
        module: true,
      },
    });

    if (!qa) {
      return NextResponse.json(
        { error: "QA not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(qa);
  } catch (error) {
    console.error("Failed to fetch QA:", error);
    return NextResponse.json(
      { error: "Failed to fetch QA" },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: idParam } = await params;
    const id = parseInt(idParam);
    
    if (isNaN(id)) {
      return NextResponse.json(
        { error: "Invalid ID" },
        { status: 400 }
      );
    }

    const formData = await request.formData();
    
    const Question = formData.get("Question") as string;
    const QuestionBy = formData.get("QuestionBy") as string;
    const Answer = formData.get("Answer") as string;
    const AnswerBy = formData.get("AnswerBy") as string;
    const project_id = parseInt(formData.get("project_id") as string);
    const status_id = parseInt(formData.get("status_id") as string);
    const module_id = parseInt(formData.get("module_id") as string);
    
    // Get module name from the selected module
    const module = await prisma.module.findUnique({
      where: { Id: module_id },
      select: { Name: true }
    });
    
    if (!module) {
      return NextResponse.json(
        { error: "Invalid module selected" },
        { status: 400 }
      );
    }
    
    const ModuleName = module.Name;
    
    const existingQuestionImages = JSON.parse(formData.get("existingQuestionImages") as string || "[]");
    const existingAnswerImages = JSON.parse(formData.get("existingAnswerImages") as string || "[]");
    
    const questionImages: string[] = [...existingQuestionImages];
    const answerImages: string[] = [...existingAnswerImages];
    
    // Handle new question images
    const questionFiles = formData.getAll("questionImages") as File[];
    for (const file of questionFiles) {
      if (file.size > 0) {
        if (file.size > 5 * 1024 * 1024) {
          return NextResponse.json(
            { error: "Image size must be less than 5MB" },
            { status: 400 }
          );
        }
        
        const fileName = `${Date.now()}-${file.name}`;
        questionImages.push(`/uploads/qa/${fileName}`);
      }
    }
    
    // Handle new answer images
    const answerFiles = formData.getAll("answerImages") as File[];
    for (const file of answerFiles) {
      if (file.size > 0) {
        if (file.size > 5 * 1024 * 1024) {
          return NextResponse.json(
            { error: "Image size must be less than 5MB" },
            { status: 400 }
          );
        }
        
        const fileName = `${Date.now()}-${file.name}`;
        answerImages.push(`/uploads/qa/${fileName}`);
      }
    }

    const updateData: any = {
      ModuleName,
      Question,
      QuestionBy,
      project: { connect: { Id: project_id } },
      status: { connect: { Id: status_id } },
      module: { connect: { Id: module_id } },
      QuestionImages: questionImages,
    };

    if (Answer) {
      updateData.Answer = Answer;
      updateData.AnswerBy = AnswerBy;
      updateData.AnswerDateTime = new Date();
      updateData.AnswerImages = answerImages;
    }

    const qa = await prisma.qA.update({
      where: { Id: id },
      data: updateData,
      include: {
        project: true,
        status: true,
        module: true,
      },
    });

    return NextResponse.json(qa);
  } catch (error) {
    console.error("Failed to update QA:", error);
    return NextResponse.json(
      { error: "Failed to update QA" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: idParam } = await params;
    const id = parseInt(idParam);
    
    if (isNaN(id)) {
      return NextResponse.json(
        { error: "Invalid ID" },
        { status: 400 }
      );
    }

    await prisma.qA.delete({
      where: { Id: id },
    });

    return NextResponse.json({ message: "QA deleted successfully" });
  } catch (error) {
    console.error("Failed to delete QA:", error);
    return NextResponse.json(
      { error: "Failed to delete QA" },
      { status: 500 }
    );
  }
}