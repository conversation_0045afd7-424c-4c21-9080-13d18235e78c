import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';

export async function saveFile(file: File, directory: string): Promise<string> {
  try {
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    // Generate unique filename
    const timestamp = Date.now();
    const fileName = `${timestamp}-${file.name}`;

    // Create the full path
    const uploadDir = join(process.cwd(), 'public', 'uploads', directory);
    const filePath = join(uploadDir, fileName);

    // Ensure directory exists
    await mkdir(uploadDir, { recursive: true });

    // Save the file
    await writeFile(filePath, buffer);

    // Return the public URL
    return `/uploads/${directory}/${fileName}`;
  } catch (error) {
    console.error('Error saving file:', error);
    throw new Error(`Failed to save file: ${file.name}`);
  }
}

export async function saveFiles(files: File[], directory: string): Promise<string[]> {
  const savedFiles: string[] = [];
  
  for (const file of files) {
    if (file.size > 0) {
      const url = await saveFile(file, directory);
      savedFiles.push(url);
    }
  }
  
  return savedFiles;
}
