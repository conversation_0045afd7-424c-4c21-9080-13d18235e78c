import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const sortBy = searchParams.get("sortBy") || "Id";
    const sortOrder = searchParams.get("sortOrder") || "asc";
    const search = searchParams.get("search") || "";
    const projectId = searchParams.get("projectId");
    
    const skip = (page - 1) * limit;
    
    const whereClause: Record<string, unknown> = {};
    
    // Add search filter
    if (search) {
      whereClause.OR = [
        { Code: { contains: search, mode: "insensitive" as const } },
        { Name: { contains: search, mode: "insensitive" as const } },
      ];
    }
    
    // Add project filter
    if (projectId) {
      whereClause.project_id = parseInt(projectId);
    }

    const [modules, total] = await Promise.all([
      prisma.module.findMany({
        where: whereClause,
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        include: {
          project: {
            select: {
              Id: true,
              Code: true,
              ProjectName: true,
            }
          }
        }
      }),
      prisma.module.count({ where: whereClause }),
    ]);

    return NextResponse.json({
      data: modules,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching modules:", error);
    return NextResponse.json(
      { error: "Failed to fetch modules" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { Code, Name, project_id } = await request.json();

    if (!Code || !Name || !project_id) {
      return NextResponse.json(
        { error: "Code, Name, and project_id are required" },
        { status: 400 }
      );
    }

    // Check if project exists
    const project = await prisma.project.findUnique({
      where: { Id: project_id }
    });

    if (!project) {
      return NextResponse.json(
        { error: "Project not found" },
        { status: 404 }
      );
    }

    const newModule = await prisma.module.create({
      data: {
        Code,
        Name,
        project_id,
      },
      include: {
        project: {
          select: {
            Id: true,
            Code: true,
            ProjectName: true,
          }
        }
      }
    });

    return NextResponse.json(newModule, { status: 201 });
  } catch (error) {
    console.error("Error creating module:", error);
    if (error && typeof error === 'object' && 'code' in error && error.code === 'P2002') {
      return NextResponse.json(
        { error: "Module code already exists" },
        { status: 409 }
      );
    }
    return NextResponse.json(
      { error: "Failed to create module" },
      { status: 500 }
    );
  }
}