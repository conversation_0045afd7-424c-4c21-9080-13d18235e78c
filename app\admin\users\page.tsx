"use client";

import { useState, useEffect, useCallback } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { UserForm } from "@/components/UserForm";
import { ConfirmDialog } from "@/components/ConfirmDialog";
import { TableSkeleton, PaginationSkeleton } from "@/components/TableSkeleton";
import { toast } from "sonner";
import { Trash2, Edit, Plus } from "lucide-react";
import { useClientOnly } from "@/hooks/useClientOnly";

interface User {
  id: string;
  user_name: string;
  full_name: string;
  email: string;
  role: "SUPER_ADMIN" | "CONSULTANT" | "CUSTOMER";
  project_id: number | null;
  project: {
    Id: number;
    Code: string;
    ProjectName: string;
  } | null;
}

interface UserResponse {
  data: User[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export default function AdminUsersPage() {
  const mounted = useClientOnly();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [sortBy, setSortBy] = useState("user_name");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const [search, setSearch] = useState("");
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  });
  const [showForm, setShowForm] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [deletingUser, setDeletingUser] = useState<User | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);

  const fetchUsers = useCallback(async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        sortBy,
        sortOrder,
        search,
      });

      const response = await fetch(`/api/admin/users?${params}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data: UserResponse = await response.json();
      
      setUsers(data?.data || []);
      setPagination(data?.pagination || {
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 0,
      });
    } catch (error) {
      console.error("Failed to fetch users:", error);
      setUsers([]);
      setPagination({
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 0,
      });
    } finally {
      setLoading(false);
    }
  }, [page, limit, sortBy, sortOrder, search]);

  useEffect(() => {
    if (mounted) {
      fetchUsers();
    }
  }, [mounted, fetchUsers]);

  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(column);
      setSortOrder("asc");
    }
  };

  const handleSearch = (value: string) => {
    setSearch(value);
    setPage(1);
  };

  const handlePageSizeChange = (value: string) => {
    setLimit(parseInt(value));
    setPage(1);
  };

  const getSortIcon = (column: string) => {
    if (sortBy !== column) return null;
    return sortOrder === "asc" ? "↑" : "↓";
  };

  const handleAddNew = () => {
    setEditingUser(null);
    setShowForm(true);
  };

  const handleEdit = (user: User) => {
    setEditingUser(user);
    setShowForm(true);
  };

  const handleDeleteClick = (user: User) => {
    setDeletingUser(user);
    setShowDeleteDialog(true);
  };

  const handleDeleteConfirm = async () => {
    if (!deletingUser) return;
    
    setDeleteLoading(true);
    try {
      const response = await fetch(`/api/admin/users/${deletingUser.id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete user");
      }

      await fetchUsers();
      setShowDeleteDialog(false);
      setDeletingUser(null);
      toast.success("User deleted successfully!");
    } catch (error) {
      console.error("Failed to delete user:", error);
      toast.error("Failed to delete user. Please try again.");
    } finally {
      setDeleteLoading(false);
    }
  };

  const handleFormSuccess = () => {
    fetchUsers();
    setShowForm(false);
    setEditingUser(null);
  };

  if (!mounted) {
    return (
      <div className="container mx-auto p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <h1 className="text-2xl sm:text-3xl font-bold">User Management</h1>
          <Button disabled className="w-full sm:w-auto">
            <Plus className="h-4 w-4 mr-2" />
            Add New User
          </Button>
        </div>
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex-1">
            <Input placeholder="Search by username, name or email..." disabled />
          </div>
          <Select disabled>
            <SelectTrigger className="w-full sm:w-40">
              <SelectValue placeholder="10 per page" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10 per page</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <TableSkeleton rows={limit} />
        <PaginationSkeleton />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 sm:p-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <h1 className="text-2xl sm:text-3xl font-bold">User Management</h1>
        <Button onClick={handleAddNew} className="w-full sm:w-auto">
          <Plus className="h-4 w-4 mr-2" />
          Add New User
        </Button>
      </div>

      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="flex-1">
          <Input
            placeholder="Search by username, name or email..."
            value={search}
            onChange={(e) => handleSearch(e.target.value)}
          />
        </div>
        <Select value={limit.toString()} onValueChange={handlePageSizeChange}>
          <SelectTrigger className="w-full sm:w-40">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="5">5 per page</SelectItem>
            <SelectItem value="10">10 per page</SelectItem>
            <SelectItem value="25">25 per page</SelectItem>
            <SelectItem value="50">50 per page</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="border rounded-lg overflow-hidden" suppressHydrationWarning>
        {!mounted || loading ? (
          <TableSkeleton rows={limit} />
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead 
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort("user_name")}
                  >
                    Username {getSortIcon("user_name")}
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort("full_name")}
                  >
                    Full Name {getSortIcon("full_name")}
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort("email")}
                  >
                    Email {getSortIcon("email")}
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort("role")}
                  >
                    Role {getSortIcon("role")}
                  </TableHead>
                  <TableHead className="hidden md:table-cell">Project</TableHead>
                  <TableHead className="w-32">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {!users || users.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-4">
                      No users found
                    </TableCell>
                  </TableRow>
                ) : (
                  users?.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell className="font-medium">{user.user_name}</TableCell>
                      <TableCell>{user.full_name}</TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          user.role === 'SUPER_ADMIN' 
                            ? 'bg-red-100 text-red-800' 
                            : user.role === 'CONSULTANT'
                            ? 'bg-blue-100 text-blue-800'
                            : 'bg-green-100 text-green-800'
                        }`}>
                          {user.role === 'SUPER_ADMIN' ? 'Super Admin' : 
                           user.role === 'CONSULTANT' ? 'Consultant' : 'Customer'}
                        </span>
                      </TableCell>
                      <TableCell className="hidden md:table-cell">
                        {user.project ? (
                          <div className="text-sm">
                            <div className="font-medium">{user.project.Code}</div>
                            <div className="text-gray-500">{user.project.ProjectName}</div>
                          </div>
                        ) : (
                          <span className="text-gray-400">No project</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleEdit(user)}
                            className="h-8 w-8 p-0"
                          >
                            <Edit className="h-3 w-3" />
                          </Button>
                          <Button 
                            variant="destructive" 
                            size="sm"
                            onClick={() => handleDeleteClick(user)}
                            className="h-8 w-8 p-0"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        )}
      </div>

      {!mounted || loading ? (
        <PaginationSkeleton />
      ) : (
        (pagination?.totalPages || 0) > 1 && (
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mt-6">
            <div className="text-sm text-gray-600 order-2 sm:order-1">
              Showing {((pagination?.page || 1) - 1) * (pagination?.limit || 10) + 1} to{" "}
              {Math.min((pagination?.page || 1) * (pagination?.limit || 10), pagination?.total || 0)} of{" "}
              {pagination?.total || 0} results
            </div>
            <div className="flex gap-2 order-1 sm:order-2">
              <Button
                variant="outline"
                disabled={(pagination?.page || 1) === 1}
                onClick={() => setPage(page - 1)}
                size="sm"
              >
                Previous
              </Button>
              
              <div className="hidden sm:flex gap-1">
                {Array.from({ length: pagination?.totalPages || 0 }, (_, i) => i + 1)
                  .filter((pageNum) => {
                    if ((pagination?.totalPages || 0) <= 7) return true;
                    if (pageNum === 1 || pageNum === (pagination?.totalPages || 0)) return true;
                    if (Math.abs(pageNum - (pagination?.page || 1)) <= 1) return true;
                    return false;
                  })
                  .map((pageNum, index, array) => {
                    const prevPage = array[index - 1];
                    const showEllipsis = prevPage && pageNum - prevPage > 1;
                    
                    return (
                      <div key={pageNum} className="flex items-center">
                        {showEllipsis && <span className="px-2">...</span>}
                        <Button
                          variant={pageNum === (pagination?.page || 1) ? "default" : "outline"}
                          size="sm"
                          onClick={() => setPage(pageNum)}
                        >
                          {pageNum}
                        </Button>
                      </div>
                    );
                  })}
              </div>

              <div className="sm:hidden flex items-center gap-2 text-sm">
                Page {pagination?.page || 1} of {pagination?.totalPages || 0}
              </div>

              <Button
                variant="outline"
                disabled={(pagination?.page || 1) === (pagination?.totalPages || 0)}
                onClick={() => setPage(page + 1)}
                size="sm"
              >
                Next
              </Button>
            </div>
          </div>
        )
      )}

      <UserForm
        open={showForm}
        onOpenChange={setShowForm}
        user={editingUser}
        onSuccess={handleFormSuccess}
      />

      <ConfirmDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        title="Delete User"
        description={`Are you sure you want to delete the user "${deletingUser?.full_name}"? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        variant="destructive"
        onConfirm={handleDeleteConfirm}
        loading={deleteLoading}
      />
    </div>
  );
}