# Smart AIO - Google OAuth Setup Guide

## Prerequisites
- Node.js installed
- PostgreSQL database setup
- Google Cloud Console access

## Step 1: Setup Google Cloud Console

1. Go to Google Cloud Console: https://console.cloud.google.com
2. Create a new project or select existing project
3. Enable Google+ API:
   - Go to "APIs & Services" > "Library"
   - Search for "Google+ API" 
   - Click and enable it

## Step 2: Create OAuth 2.0 Credentials

1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth client ID"
3. Choose "Web application" as application type
4. Configure the OAuth consent screen first if prompted:
   - User Type: External (for testing) or Internal (for organization)
   - Fill required fields: App name, User support email, Developer email
   - Add test users if using External type
5. For OAuth client ID configuration:
   - Name: Smart AIO App (or any name)
   - Authorized JavaScript origins:
     * http://localhost:3000 (for development)
     * Your production domain (when deploying)
   - Authorized redirect URIs:
     * http://localhost:3000/api/auth/callback/google (for development)
     * https://yourdomain.com/api/auth/callback/google (for production)
6. Click "Create"
7. Copy the Client ID and Client Secret

## Step 3: Update Environment Variables

1. Open .env file in your project root
2. Replace the placeholder values:
   
   GOOGLE_CLIENT_ID="your-actual-google-client-id"
   GOOGLE_CLIENT_SECRET="your-actual-google-client-secret"
   
   Also update:
   NEXTAUTH_SECRET="generate-a-random-secret-string"
   NEXTAUTH_URL="http://localhost:3000" (or your domain)

## Step 4: Generate NextAuth Secret

Run this command to generate a secure secret:
   openssl rand -base64 32

Or use online generator: https://generate-secret.vercel.app/32

## Step 5: Setup Database

1. Make sure your PostgreSQL database is running
2. Update DATABASE_URL in .env with your actual database credentials
3. Run database migrations:
   npx prisma migrate dev --name init
4. Generate Prisma client:
   npx prisma generate

## Step 6: Install Dependencies

npm install

## Step 7: Run the Application

npm run dev

## Step 8: Test Authentication

1. Open browser: http://localhost:3000
2. You should be redirected to login page
3. Click "Sign in with Google"
4. Complete Google OAuth flow
5. You should be redirected back to the main page

## Troubleshooting

### Error: redirect_uri_mismatch
- Check that your redirect URI in Google Console exactly matches:
  http://localhost:3000/api/auth/callback/google

### Error: unauthorized_client
- Make sure OAuth consent screen is configured
- Add test users if using External user type
- Verify client ID and secret are correct

### Database Connection Issues
- Verify PostgreSQL is running
- Check DATABASE_URL format: postgresql://username:password@host:port/database
- Ensure database exists

### NextAuth Errors
- Verify NEXTAUTH_SECRET is set
- Check NEXTAUTH_URL matches your domain
- Ensure all environment variables are properly quoted

## Project Structure

/app
  /api/auth/[...nextauth]/route.ts  - NextAuth API routes
  /login/page.tsx                   - Custom login page
  /page.tsx                         - Protected main page
  /layout.tsx                       - Root layout with providers
  /providers.tsx                    - Session provider wrapper
/lib
  /auth.ts                          - NextAuth configuration
/prisma
  /schema.prisma                    - Database schema
/middleware.ts                      - Route protection middleware
/.env                               - Environment variables

## Security Notes

- Never commit .env file to version control
- Use strong, unique NEXTAUTH_SECRET
- In production, use HTTPS URLs
- Regularly rotate client secrets
- Review OAuth scopes and permissions

## Production Deployment

1. Update NEXTAUTH_URL to your production domain
2. Add production domain to Google OAuth settings
3. Use production database URL
4. Set all environment variables on your hosting platform
5. Ensure HTTPS is enabled

## Support

For issues:
- Check Google Cloud Console error logs
- Review NextAuth documentation: https://next-auth.js.org
- Verify environment variables are loaded correctly