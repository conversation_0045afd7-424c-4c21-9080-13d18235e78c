import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const sortBy = searchParams.get("sortBy") || "Id";
    const sortOrder = searchParams.get("sortOrder") || "asc";
    const search = searchParams.get("search") || "";
    
    const skip = (page - 1) * limit;
    
    const whereClause = search
      ? {
          OR: [
            { Code: { contains: search, mode: "insensitive" as const } },
            { ProjectName: { contains: search, mode: "insensitive" as const } },
            { CustomerName: { contains: search, mode: "insensitive" as const } },
          ],
        }
      : {};

    const [projects, total] = await Promise.all([
      prisma.project.findMany({
        where: whereClause,
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
      }),
      prisma.project.count({ where: whereClause }),
    ]);

    return NextResponse.json({
      data: projects,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch {
    return NextResponse.json(
      { error: "Failed to fetch projects" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { Code, ProjectName, CustomerName, StartDate, ExpiredDate, Period } = await request.json();

    if (!Code || !ProjectName || !CustomerName || !StartDate || !ExpiredDate || Period === undefined) {
      return NextResponse.json(
        { error: "All fields are required" },
        { status: 400 }
      );
    }

    const project = await prisma.project.create({
      data: {
        Code,
        ProjectName,
        CustomerName,
        StartDate: new Date(StartDate),
        ExpiredDate: new Date(ExpiredDate),
        Period,
      },
    });

    return NextResponse.json(project, { status: 201 });
  } catch {
    return NextResponse.json(
      { error: "Failed to create project" },
      { status: 500 }
    );
  }
}