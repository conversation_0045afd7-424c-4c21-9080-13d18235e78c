import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const sortBy = searchParams.get("sortBy") || "Id";
    const sortOrder = searchParams.get("sortOrder") || "asc";
    const search = searchParams.get("search") || "";
    
    const skip = (page - 1) * limit;
    
    const whereClause = search
      ? {
          OR: [
            { Code: { contains: search, mode: "insensitive" as const } },
            { StatusName: { contains: search, mode: "insensitive" as const } },
          ],
        }
      : {};

    const [statuses, total] = await Promise.all([
      prisma.status.findMany({
        where: whereClause,
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
      }),
      prisma.status.count({ where: whereClause }),
    ]);

    return NextResponse.json({
      data: statuses,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch {
    return NextResponse.json(
      { error: "Failed to fetch statuses" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { Code, StatusName } = await request.json();

    if (!Code || !StatusName) {
      return NextResponse.json(
        { error: "Code and StatusName are required" },
        { status: 400 }
      );
    }

    const status = await prisma.status.create({
      data: {
        Code,
        StatusName,
      },
    });

    return NextResponse.json(status, { status: 201 });
  } catch {
    return NextResponse.json(
      { error: "Failed to create status" },
      { status: 500 }
    );
  }
}