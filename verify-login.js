const { exec } = require('child_process');

async function testLogin() {
    console.log('🔍 Testing login functionality...\n');

    // Test 1: Check if login page loads
    console.log('1. Testing login page...');
    const loginResponse = await fetch('http://localhost:3000/login');
    if (loginResponse.ok) {
        console.log('✅ Login page loads successfully');
    } else {
        console.log('❌ Login page failed to load');
        return;
    }

    // Test 2: Check if providers are configured
    console.log('\n2. Testing authentication providers...');
    const providersResponse = await fetch('http://localhost:3000/api/auth/providers');
    const providers = await providersResponse.json();
    
    if (providers.google && providers.credentials) {
        console.log('✅ Google OAuth provider configured');
        console.log('✅ Credentials provider configured');
    } else {
        console.log('❌ Providers not properly configured');
    }

    // Test 3: Test user registration
    console.log('\n3. Testing user registration...');
    const registerData = {
        username: 'testuser' + Date.now(),
        password: 'testpass123',
        email: 'test' + Date.now() + '@example.com',
        fullName: 'Test User'
    };

    const registerResponse = await fetch('http://localhost:3000/api/auth/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(registerData)
    });

    if (registerResponse.ok) {
        const result = await registerResponse.json();
        console.log('✅ User registration successful');
        console.log('   User ID:', result.user.id);
        console.log('   Username:', result.user.user_name);
        console.log('   Role:', result.user.role);

        // Test 4: Test credentials login
        console.log('\n4. Testing credentials login...');
        
        // Get CSRF token first
        const csrfResponse = await fetch('http://localhost:3000/api/auth/csrf');
        const { csrfToken } = await csrfResponse.json();
        
        const loginData = new URLSearchParams({
            username: registerData.username,
            password: registerData.password,
            csrfToken: csrfToken,
            callbackUrl: 'http://localhost:3000/',
            json: 'true'
        });

        const loginResponse = await fetch('http://localhost:3000/api/auth/signin/credentials', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: loginData
        });

        const loginResult = await loginResponse.json();
        
        if (loginResult.url && loginResult.url.includes('localhost:3000')) {
            console.log('✅ Credentials login successful');
            console.log('   Redirect URL:', loginResult.url);
        } else if (loginResult.error) {
            console.log('❌ Login failed:', loginResult.error);
        } else {
            console.log('⚠️  Login response:', loginResult);
        }

    } else {
        const error = await registerResponse.json();
        console.log('❌ User registration failed:', error.error);
    }

    console.log('\n🎉 Login functionality testing complete!');
}

// Run the test
testLogin().catch(console.error);