"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { ConfirmDialog } from "@/components/ConfirmDialog";
import { TableSkeleton, PaginationSkeleton } from "@/components/TableSkeleton";
import { toast } from "sonner";
import { Trash2, Edit, Plus, Image } from "lucide-react";
import { useClientOnly } from "@/hooks/useClientOnly";

interface QA {
  Id: number;
  ModuleName: string;
  Question: string;
  QuestionBy: string;
  QuestionDateTime: string;
  Answer: string | null;
  AnswerBy: string | null;
  AnswerDateTime: string | null;
  ImageUrl: string | null;
  QuestionImages: string[];
  AnswerImages: string[];
  project_id: number;
  status_id: number;
  module_id: number;
  project: {
    Id: number;
    ProjectName: string;
  };
  status: {
    Id: number;
    StatusName: string;
  };
  module: {
    Id: number;
    Name: string;
  };
}

interface QAResponse {
  data: QA[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export default function QAPage() {
  const mounted = useClientOnly();
  const router = useRouter();
  const [qas, setQAs] = useState<QA[]>([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [sortBy, setSortBy] = useState("Id");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [search, setSearch] = useState("");
  const [projectFilter, setProjectFilter] = useState("all");
  const [projects, setProjects] = useState<Array<{Id: number, ProjectName: string}>>([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  });
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [deletingQA, setDeletingQA] = useState<QA | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);

  const fetchQAs = useCallback(async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        sortBy,
        sortOrder,
        search,
      });

      if (projectFilter && projectFilter !== "all") {
        params.append("project", projectFilter);
      }

      const response = await fetch(`/api/qa?${params}`);
      const data: QAResponse = await response.json();
      
      setQAs(data.data);
      setPagination(data.pagination);
    } catch (error) {
      console.error("Failed to fetch QAs:", error);
      toast.error("Failed to fetch QAs");
    } finally {
      setLoading(false);
    }
  }, [page, limit, sortBy, sortOrder, search, projectFilter]);

  const fetchProjects = useCallback(async () => {
    try {
      const response = await fetch("/api/qa/dropdown-data");
      if (!response.ok) {
        throw new Error("Failed to fetch projects");
      }
      const data = await response.json();
      setProjects(data.projects);
    } catch (error) {
      console.error("Failed to fetch projects:", error);
      toast.error("Failed to fetch projects");
    }
  }, []);

  useEffect(() => {
    if (mounted) {
      fetchQAs();
      fetchProjects();
    }
  }, [mounted, fetchQAs, fetchProjects]);

  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(column);
      setSortOrder("asc");
    }
  };

  const handleSearch = (value: string) => {
    setSearch(value);
    setPage(1);
  };

  const handlePageSizeChange = (value: string) => {
    setLimit(parseInt(value));
    setPage(1);
  };

  const handleProjectFilterChange = (value: string) => {
    setProjectFilter(value);
    setPage(1);
  };

  const getSortIcon = (column: string) => {
    if (sortBy !== column) return null;
    return sortOrder === "asc" ? "↑" : "↓";
  };

  const handleAddNew = () => {
    router.push("/qa/new");
  };

  const handleEdit = (qa: QA) => {
    router.push(`/qa/new?edit=${qa.Id}`);
  };

  const handleDeleteClick = (qa: QA) => {
    setDeletingQA(qa);
    setShowDeleteDialog(true);
  };

  const handleDeleteConfirm = async () => {
    if (!deletingQA) return;
    
    setDeleteLoading(true);
    try {
      const response = await fetch(`/api/qa/${deletingQA.Id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete QA");
      }

      await fetchQAs();
      setShowDeleteDialog(false);
      setDeletingQA(null);
      toast.success("QA deleted successfully!");
    } catch (error) {
      console.error("Failed to delete QA:", error);
      toast.error("Failed to delete QA. Please try again.");
    } finally {
      setDeleteLoading(false);
    }
  };



  const formatDate = (dateString: string | null) => {
    if (!dateString) return "-";
    return new Date(dateString).toLocaleDateString();
  };

  const hasImages = (qa: QA) => {
    return (qa.QuestionImages && qa.QuestionImages.length > 0) || 
           (qa.AnswerImages && qa.AnswerImages.length > 0) ||
           qa.ImageUrl;
  };

  if (!mounted) {
    return (
      <div className="container mx-auto p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <h1 className="text-2xl sm:text-3xl font-bold">Questions & Answers</h1>
          <Button disabled className="w-full sm:w-auto">
            <Plus className="h-4 w-4 mr-2" />
            Ask Question
          </Button>
        </div>
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex-1">
            <Input placeholder="Search questions, answers, modules..." disabled />
          </div>
          <Select disabled>
            <SelectTrigger className="w-full sm:w-48">
              <SelectValue placeholder="All Projects" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Projects</SelectItem>
            </SelectContent>
          </Select>
          <Select disabled>
            <SelectTrigger className="w-full sm:w-40">
              <SelectValue placeholder="10 per page" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10 per page</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <TableSkeleton rows={limit} />
        <PaginationSkeleton />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 sm:p-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <h1 className="text-2xl sm:text-3xl font-bold">Questions & Answers</h1>
        <Button onClick={handleAddNew} className="w-full sm:w-auto">
          <Plus className="h-4 w-4 mr-2" />
          Ask Question
        </Button>
      </div>

      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="flex-1">
          <Input
            placeholder="Search questions, answers, modules..."
            value={search}
            onChange={(e) => handleSearch(e.target.value)}
          />
        </div>
        <Select value={projectFilter} onValueChange={handleProjectFilterChange}>
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue placeholder="All Projects" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Projects</SelectItem>
            {projects.map((project) => (
              <SelectItem key={project.Id} value={project.Id.toString()}>
                {project.ProjectName}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Select value={limit.toString()} onValueChange={handlePageSizeChange}>
          <SelectTrigger className="w-full sm:w-40">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="5">5 per page</SelectItem>
            <SelectItem value="10">10 per page</SelectItem>
            <SelectItem value="25">25 per page</SelectItem>
            <SelectItem value="50">50 per page</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="border rounded-lg overflow-hidden" suppressHydrationWarning>
        {!mounted || loading ? (
          <TableSkeleton rows={limit} />
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead 
                    className="cursor-pointer hover:bg-gray-50 w-16"
                    onClick={() => handleSort("Id")}
                  >
                    ID {getSortIcon("Id")}
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort("project")}
                  >
                    Project {getSortIcon("project")}
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort("module")}
                  >
                    Module {getSortIcon("module")}
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort("Question")}
                  >
                    Question {getSortIcon("Question")}
                  </TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort("QuestionBy")}
                  >
                    Asked By {getSortIcon("QuestionBy")}
                  </TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Images</TableHead>
                  <TableHead 
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort("QuestionDateTime")}
                  >
                    Date {getSortIcon("QuestionDateTime")}
                  </TableHead>
                  <TableHead className="w-32">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {qas.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={9} className="text-center py-4">
                      No Q&As found
                    </TableCell>
                  </TableRow>
                ) : (
                  qas.map((qa) => (
                    <TableRow key={qa.Id}>
                      <TableCell>{qa.Id}</TableCell>
                      <TableCell className="max-w-[150px] truncate">{qa.project.ProjectName}</TableCell>
                      <TableCell className="max-w-[150px] truncate">{qa.module.Name}</TableCell>
                      <TableCell className="max-w-[200px] truncate" title={qa.Question}>
                        {qa.Question}
                      </TableCell>
                      <TableCell>{qa.QuestionBy}</TableCell>
                      <TableCell>
                        <Badge variant={qa.Answer ? "default" : "secondary"}>
                          {qa.status.StatusName}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {hasImages(qa) && (
                          <Image className="h-4 w-4 text-blue-500" />
                        )}
                      </TableCell>
                      <TableCell>{formatDate(qa.QuestionDateTime)}</TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleEdit(qa)}
                            className="h-8 w-8 p-0"
                          >
                            <Edit className="h-3 w-3" />
                          </Button>
                          <Button 
                            variant="destructive" 
                            size="sm"
                            onClick={() => handleDeleteClick(qa)}
                            className="h-8 w-8 p-0"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        )}
      </div>

      {!mounted || loading ? (
        <PaginationSkeleton />
      ) : (
        pagination.totalPages > 1 && (
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mt-6">
            <div className="text-sm text-gray-600 order-2 sm:order-1">
              Showing {(pagination.page - 1) * pagination.limit + 1} to{" "}
              {Math.min(pagination.page * pagination.limit, pagination.total)} of{" "}
              {pagination.total} results
            </div>
            <div className="flex gap-2 order-1 sm:order-2">
              <Button
                variant="outline"
                disabled={pagination.page === 1}
                onClick={() => setPage(page - 1)}
                size="sm"
              >
                Previous
              </Button>
              
              <div className="hidden sm:flex gap-1">
                {Array.from({ length: pagination.totalPages }, (_, i) => i + 1)
                  .filter((pageNum) => {
                    if (pagination.totalPages <= 7) return true;
                    if (pageNum === 1 || pageNum === pagination.totalPages) return true;
                    if (Math.abs(pageNum - pagination.page) <= 1) return true;
                    return false;
                  })
                  .map((pageNum, index, array) => {
                    const prevPage = array[index - 1];
                    const showEllipsis = prevPage && pageNum - prevPage > 1;
                    
                    return (
                      <div key={pageNum} className="flex items-center">
                        {showEllipsis && <span className="px-2">...</span>}
                        <Button
                          variant={pageNum === pagination.page ? "default" : "outline"}
                          size="sm"
                          onClick={() => setPage(pageNum)}
                        >
                          {pageNum}
                        </Button>
                      </div>
                    );
                  })}
              </div>

              <div className="sm:hidden flex items-center gap-2 text-sm">
                Page {pagination.page} of {pagination.totalPages}
              </div>

              <Button
                variant="outline"
                disabled={pagination.page === pagination.totalPages}
                onClick={() => setPage(page + 1)}
                size="sm"
              >
                Next
              </Button>
            </div>
          </div>
        )
      )}

      <ConfirmDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        title="Delete Q&A"
        description={`Are you sure you want to delete this Q&A? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        variant="destructive"
        onConfirm={handleDeleteConfirm}
        loading={deleteLoading}
      />
    </div>
  );
}