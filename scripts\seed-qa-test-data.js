const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  try {
    // Create or get test project
    let project = await prisma.project.findFirst({
      where: { Code: 'TEST_PROJECT_QA' }
    });
    
    if (!project) {
      project = await prisma.project.create({
        data: {
          Code: 'TEST_PROJECT_QA',
          ProjectName: 'Test Project for QA',
          CustomerName: 'Test Customer',
          StartDate: new Date(),
          ExpiredDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
          Period: 12,
        }
      });
    }

    // Create or get test module
    let module = await prisma.module.findFirst({
      where: { Code: 'TEST_MODULE_QA' }
    });
    
    if (!module) {
      module = await prisma.module.create({
        data: {
          Code: 'TEST_MODULE_QA',
          Name: 'Test Module for QA',
          project_id: project.Id,
        }
      });
    }

    // Create or get test status
    let status = await prisma.status.findFirst({
      where: { Code: 'OPEN' }
    });
    
    if (!status) {
      status = await prisma.status.create({
        data: {
          Code: 'OPEN',
          StatusName: 'Open',
        }
      });
    }

    // Create test QA entries
    const qa1 = await prisma.qA.create({
      data: {
        ModuleName: 'Authentication Module',
        Question: 'How do I implement user login functionality?',
        QuestionBy: 'John Doe',
        Answer: 'You can use NextAuth.js for authentication.',
        AnswerBy: 'Jane Smith',
        AnswerDateTime: new Date(),
        QuestionImages: [],
        AnswerImages: [],
        project_id: project.Id,
        status_id: status.Id,
        module_id: module.Id,
      }
    });

    const qa2 = await prisma.qA.create({
      data: {
        ModuleName: 'Database Module', 
        Question: 'What database should I use for this project?',
        QuestionBy: 'Alice Johnson',
        QuestionImages: ['/uploads/qa/sample-diagram.png'],
        AnswerImages: [],
        project_id: project.Id,
        status_id: status.Id,
        module_id: module.Id,
      }
    });

    console.log('Test data created successfully!');
    console.log(`Project: ${project.ProjectName} (ID: ${project.Id})`);
    console.log(`Module: ${module.Name} (ID: ${module.Id})`);
    console.log(`Status: ${status.StatusName} (ID: ${status.Id})`);
    console.log(`QA entries: ${qa1.Id}, ${qa2.Id}`);
    
  } catch (error) {
    console.error('Error seeding test data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();