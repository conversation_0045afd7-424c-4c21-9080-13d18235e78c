generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Project {
  Id           Int      @id @default(autoincrement())
  Code         String   @unique
  ProjectName  String
  CustomerName String
  StartDate    DateTime
  ExpiredDate  DateTime
  Period       Int
  modules      Module[]
  qas          QA[]
  users        User[]

  @@map("projects")
}

model Status {
  Id         Int    @id @default(autoincrement())
  Code       String @unique
  StatusName String
  qas        QA[]

  @@map("statuses")
}

model Module {
  Id         Int     @id @default(autoincrement())
  Code       String  @unique
  Name       String
  project_id Int
  project    Project @relation(fields: [project_id], references: [Id])
  qas        QA[]

  @@map("modules")
}

model QA {
  Id               Int       @id @default(autoincrement())
  ModuleName       String
  Question         String
  QuestionBy       String
  QuestionDateTime DateTime  @default(now())
  Answer           String?
  AnswerBy         String?
  AnswerDateTime   DateTime?
  project_id       Int
  status_id        Int
  module_id        Int
  AnswerImages     String[]
  ImageUrl         String?
  QuestionImages   String[]
  module           Module    @relation(fields: [module_id], references: [Id])
  project          Project   @relation(fields: [project_id], references: [Id])
  status           Status    @relation(fields: [status_id], references: [Id])

  @@map("qas")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  full_name     String?
  password      String?
  project_id    Int?
  role          UserRole  @default(CUSTOMER)
  user_name     String?   @unique
  accounts      Account[]
  sessions      Session[]
  project       Project?  @relation(fields: [project_id], references: [Id])

  @@map("users")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

enum UserRole {
  SUPER_ADMIN
  CONSULTANT
  CUSTOMER

  @@map("user_roles")
}
