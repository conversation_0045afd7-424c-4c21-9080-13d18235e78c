const { PrismaClient } = require('../lib/generated/prisma');

const prisma = new PrismaClient();

async function main() {
  console.log('Seeding project data...');
  
  const projects = [
    {
      Code: 'PROJ001',
      ProjectName: 'E-Commerce Platform',
      CustomerName: 'TechCorp Ltd.',
      StartDate: new Date('2024-01-15'),
      ExpiredDate: new Date('2024-12-31'),
      Period: 12
    },
    {
      Code: 'PROJ002',
      ProjectName: 'Mobile Banking App',
      CustomerName: 'FinanceBank Inc.',
      StartDate: new Date('2024-03-01'),
      ExpiredDate: new Date('2025-02-28'),
      Period: 12
    },
    {
      Code: 'PROJ003',
      ProjectName: 'HR Management System',
      CustomerName: 'GlobalTech Solutions',
      StartDate: new Date('2024-06-01'),
      ExpiredDate: new Date('2024-11-30'),
      Period: 6
    },
    {
      Code: 'PROJ004',
      ProjectName: 'Inventory Management',
      CustomerName: 'Retail<PERSON>hain Co.',
      StartDate: new Date('2024-02-01'),
      ExpiredDate: new Date('2025-01-31'),
      Period: 12
    }
  ];

  for (const project of projects) {
    try {
      await prisma.project.upsert({
        where: { Code: project.Code },
        update: {},
        create: project,
      });
      console.log(`Created/Updated project: ${project.Code} - ${project.ProjectName}`);
    } catch (error) {
      console.log(`Project ${project.Code} might already exist:`, error.message);
    }
  }
  
  console.log('Project seeding completed!');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });