const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function initializeDatabase() {
  try {
    console.log('🚀 Initializing database...');

    // Hash the password for security
    const hashedPassword = await bcrypt.hash('122333444', 10);

    // Create the user
    console.log('👤 Creating user...');
    const user = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {
        user_name: 'trungbq',
        password: hashedPassword,
        full_name: '<PERSON>',
        name: '<PERSON>',
        role: 'SUPER_ADMIN'
      },
      create: {
        user_name: 'trungbq',
        email: '<EMAIL>',
        password: hashedPassword,
        full_name: '<PERSON>',
        name: '<PERSON>',
        role: 'SUPER_ADMIN'
      }
    });
    console.log('✅ User created:', user.email);

    // Create statuses
    console.log('📋 Creating statuses...');
    const statuses = [
      { Code: 'ACTIVE', StatusName: 'Active' },
      { Code: 'PENDING', StatusName: 'Pending' },
      { Code: 'ON_HOLD', StatusName: 'On hold' },
      { Code: 'KIV', StatusName: 'KIV' },
      { Code: 'DONE', StatusName: 'Done' }
    ];

    for (const status of statuses) {
      const createdStatus = await prisma.status.upsert({
        where: { Code: status.Code },
        update: {
          StatusName: status.StatusName
        },
        create: {
          Code: status.Code,
          StatusName: status.StatusName
        }
      });
      console.log(`✅ Status created: ${createdStatus.StatusName}`);
    }

    // Create default project if it doesn't exist
    console.log('🏗️ Creating default project...');
    const defaultProject = await prisma.project.upsert({
      where: { Code: 'DEFAULT_PROJECT' },
      update: {
        ProjectName: 'Default Project',
        CustomerName: 'Default Customer'
      },
      create: {
        Code: 'DEFAULT_PROJECT',
        ProjectName: 'Default Project',
        CustomerName: 'Default Customer',
        StartDate: new Date(),
        ExpiredDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
        Period: 12
      }
    });
    console.log(`✅ Project created: ${defaultProject.ProjectName}`);

    // Create default modules
    console.log('📦 Creating default modules...');
    const modules = [
      { Code: 'AUTH', Name: 'Authentication' },
      { Code: 'USER_MGMT', Name: 'User Management' },
      { Code: 'DASHBOARD', Name: 'Dashboard' },
      { Code: 'REPORTS', Name: 'Reports' }
    ];

    for (const moduleData of modules) {
      const createdModule = await prisma.module.upsert({
        where: { Code: moduleData.Code },
        update: {
          Name: moduleData.Name,
          project_id: defaultProject.Id
        },
        create: {
          Code: moduleData.Code,
          Name: moduleData.Name,
          project_id: defaultProject.Id
        }
      });
      console.log(`✅ Module created: ${createdModule.Name}`);
    }

    console.log('🎉 Database initialization completed successfully!');
    
    // Display summary
    console.log('\n📊 Summary:');
    console.log(`User: ${user.user_name} (${user.email}) - Role: ${user.role}`);
    console.log(`Project: ${defaultProject.ProjectName}`);
    console.log(`Statuses: ${statuses.length} statuses created`);
    console.log(`Modules: ${modules.length} modules created`);

  } catch (error) {
    console.error('❌ Error initializing database:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the initialization
if (require.main === module) {
  initializeDatabase();
}

module.exports = { initializeDatabase };