"use client";

import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { toast } from "sonner";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

const userSchema = z.object({
  user_name: z.string().min(1, "Username is required").max(50, "Username must be less than 50 characters"),
  password: z.string().optional(),
  full_name: z.string().min(1, "Full name is required").max(100, "Full name must be less than 100 characters"),
  email: z.string().email("Invalid email format").optional().or(z.literal("")),
  role: z.enum(["SUPER_ADMIN", "CONSULTANT", "CUSTOMER"]),
  project_id: z.string().optional(),
});

type UserFormData = z.infer<typeof userSchema>;

interface User {
  id: string;
  user_name: string;
  full_name: string;
  email: string;
  role: "SUPER_ADMIN" | "CONSULTANT" | "CUSTOMER";
  project_id: number | null;
  project: {
    Id: number;
    Code: string;
    ProjectName: string;
  } | null;
}

interface Project {
  Id: number;
  Code: string;
  ProjectName: string;
}

interface UserFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  user?: User | null;
  onSuccess: () => void;
}

export function UserForm({ open, onOpenChange, user, onSuccess }: UserFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [projects, setProjects] = useState<Project[]>([]);
  const isEditing = !!user;

  const form = useForm<UserFormData>({
    resolver: zodResolver(userSchema),
    defaultValues: {
      user_name: "",
      password: "",
      full_name: "",
      email: "",
      role: "CUSTOMER" as const,
      project_id: "none",
    },
  });

  // Fetch projects for dropdown
  useEffect(() => {
    const fetchProjects = async () => {
      try {
        const response = await fetch("/api/admin/projects?limit=100");
        if (response.ok) {
          const result = await response.json();
          setProjects(result.data || []);
        }
      } catch (error) {
        console.error("Error fetching projects:", error);
      }
    };

    fetchProjects();
  }, []);

  // Reset form when user changes or dialog opens
  React.useEffect(() => {
    if (open) {
      form.reset({
        user_name: user?.user_name || "",
        password: "",
        full_name: user?.full_name || "",
        email: user?.email || "",
        role: user?.role || "CUSTOMER",
        project_id: user?.project_id ? String(user.project_id) : "none",
      });
    }
  }, [open, user, form]);

  const onSubmit = async (data: UserFormData) => {
    setIsLoading(true);
    try {
      // Validate password for new users
      if (!isEditing && (!data.password || data.password.length < 6)) {
        toast.error("Password must be at least 6 characters for new users");
        setIsLoading(false);
        return;
      }
      
      const url = isEditing 
        ? `/api/admin/users/${user.id}`
        : `/api/admin/users`;
      
      const method = isEditing ? "PUT" : "POST";
      
      const submitData = {
        ...data,
        project_id: data.project_id && data.project_id !== "none" ? parseInt(data.project_id) : null,
      };

      // If editing and password is empty, don't send password
      if (isEditing && !data.password) {
        delete (submitData as Record<string, string | number | null>).password;
      }
      
      console.log('Submitting user data:', submitData);
      
      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(submitData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: `HTTP ${response.status}: ${response.statusText}` }));
        console.error('API Error Response:', errorData);
        throw new Error(errorData.error || `Failed to save user (${response.status})`);
      }

      form.reset();
      onOpenChange(false);
      onSuccess();
      
      toast.success(
        isEditing 
          ? "User updated successfully!" 
          : "User created successfully!"
      );
    } catch (error) {
      console.error("Error saving user:", error);
      toast.error(
        error instanceof Error 
          ? error.message 
          : "Failed to save user. Please try again."
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      form.reset();
    }
    onOpenChange(newOpen);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? "Edit User" : "Add New User"}
          </DialogTitle>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="user_name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Username</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="Enter username" 
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Password {isEditing && "(leave empty to keep current)"}
                  </FormLabel>
                  <FormControl>
                    <Input 
                      type="password"
                      placeholder={isEditing ? "Enter new password" : "Enter password"}
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="full_name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Full Name</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="Enter full name" 
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="role"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Role</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger disabled={isLoading}>
                        <SelectValue placeholder="Select user role" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="SUPER_ADMIN">Super Admin</SelectItem>
                      <SelectItem value="CONSULTANT">Consultant</SelectItem>
                      <SelectItem value="CUSTOMER">Customer</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input 
                      type="email"
                      placeholder="Enter email address" 
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="project_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Project</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger disabled={isLoading}>
                        <SelectValue placeholder="Select a project (optional)" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="none">No project</SelectItem>
                      {projects.map((project) => (
                        <SelectItem key={project.Id} value={String(project.Id)}>
                          {project.Code} - {project.ProjectName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <div className="flex justify-end gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => handleOpenChange(false)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? "Saving..." : isEditing ? "Update" : "Create"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}