import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import bcrypt from "bcryptjs";

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const sortBy = searchParams.get("sortBy") || "id";
    const sortOrder = searchParams.get("sortOrder") || "asc";
    const search = searchParams.get("search") || "";
    
    const skip = (page - 1) * limit;
    
    const whereClause = search
      ? {
          OR: [
            { user_name: { contains: search, mode: "insensitive" as const } },
            { full_name: { contains: search, mode: "insensitive" as const } },
            { email: { contains: search, mode: "insensitive" as const } },
          ],
        }
      : {};

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where: whereClause,
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        include: {
          project: {
            select: {
              Id: true,
              Code: true,
              ProjectName: true,
            }
          }
        }
      }),
      prisma.user.count({ where: whereClause }),
    ]);

    return NextResponse.json({
      data: users,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: "Failed to fetch users" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { user_name, password, full_name, project_id, email, role } = await request.json();

    if (!user_name || !password || !full_name || !role) {
      return NextResponse.json(
        { error: "user_name, password, full_name, and role are required" },
        { status: 400 }
      );
    }

    const hashedPassword = await bcrypt.hash(password, 12);

    const user = await prisma.user.create({
      data: {
        user_name,
        password: hashedPassword,
        full_name,
        role,
        project_id: project_id || null,
        email: email || `${user_name}@temp.local`,
      },
      include: {
        project: {
          select: {
            Id: true,
            Code: true,
            ProjectName: true,
          }
        }
      }
    });

    return NextResponse.json(user, { status: 201 });
  } catch (error) {
    console.error('Error creating user:', error);
    
    // Handle specific Prisma errors
    if (error instanceof Error) {
      // Handle unique constraint violations
      if (error.message.includes('Unique constraint')) {
        if (error.message.includes('user_name')) {
          return NextResponse.json(
            { error: "Username already exists" },
            { status: 409 }
          );
        }
        if (error.message.includes('email')) {
          return NextResponse.json(
            { error: "Email already exists" },
            { status: 409 }
          );
        }
      }
      
      // Return specific error message for debugging
      return NextResponse.json(
        { error: `Failed to create user: ${error.message}` },
        { status: 500 }
      );
    }
    
    return NextResponse.json(
      { error: "Failed to create user" },
      { status: 500 }
    );
  }
}