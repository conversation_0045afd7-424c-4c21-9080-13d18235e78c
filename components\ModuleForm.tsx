"use client";

import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { toast } from "sonner";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";

const moduleSchema = z.object({
  Code: z.string().min(1, "Code is required").max(50, "Code must be less than 50 characters"),
  Name: z.string().min(1, "Name is required").max(100, "Name must be less than 100 characters"),
  project_id: z.number().min(1, "Project is required"),
});

type ModuleFormData = z.infer<typeof moduleSchema>;

interface Module {
  Id: number;
  Code: string;
  Name: string;
  project_id: number;
  project?: {
    Id: number;
    Code: string;
    ProjectName: string;
  };
}

interface Project {
  Id: number;
  Code: string;
  ProjectName: string;
}

interface ModuleFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  module?: Module | null;
  onSuccess: () => void;
}

export function ModuleForm({ open, onOpenChange, module, onSuccess }: ModuleFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [projects, setProjects] = useState<Project[]>([]);
  const [projectsLoading, setProjectsLoading] = useState(false);
  const isEditing = !!module;

  const form = useForm<ModuleFormData>({
    resolver: zodResolver(moduleSchema),
    defaultValues: {
      Code: "",
      Name: "",
      project_id: 0,
    },
  });

  // Fetch projects for dropdown
  const fetchProjects = async () => {
    setProjectsLoading(true);
    try {
      const response = await fetch("/api/admin/projects");
      if (response.ok) {
        const data = await response.json();
        setProjects(data);
      }
    } catch (error) {
      console.error("Failed to fetch projects:", error);
      toast.error("Failed to load projects");
    } finally {
      setProjectsLoading(false);
    }
  };

  // Reset form when module changes or dialog opens
  useEffect(() => {
    if (open) {
      form.reset({
        Code: module?.Code || "",
        Name: module?.Name || "",
        project_id: module?.project_id || 0,
      });
      fetchProjects();
    }
  }, [open, module, form]);

  const onSubmit = async (data: ModuleFormData) => {
    setIsLoading(true);
    try {
      const url = isEditing 
        ? `/api/admin/modules/${module.Id}`
        : `/api/admin/modules`;
      
      const method = isEditing ? "PUT" : "POST";
      
      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to save module");
      }

      form.reset();
      onOpenChange(false);
      onSuccess();
      
      toast.success(
        isEditing 
          ? "Module updated successfully!" 
          : "Module created successfully!"
      );
    } catch (error) {
      console.error("Error saving module:", error);
      toast.error(
        error instanceof Error 
          ? error.message 
          : "Failed to save module. Please try again."
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      form.reset();
    }
    onOpenChange(newOpen);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? "Edit Module" : "Add New Module"}
          </DialogTitle>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="Code"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Code</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="Enter module code" 
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="Name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="Enter module name" 
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="project_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Project</FormLabel>
                  <FormControl>
                    {projectsLoading ? (
                      <Skeleton className="h-10 w-full" />
                    ) : (
                      <Select
                        value={field.value ? field.value.toString() : ""}
                        onValueChange={(value) => field.onChange(parseInt(value))}
                        disabled={isLoading}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select a project" />
                        </SelectTrigger>
                        <SelectContent>
                          {projects.map((project) => (
                            <SelectItem 
                              key={project.Id} 
                              value={project.Id.toString()}
                            >
                              {project.ProjectName} ({project.Code})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    )}
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <div className="flex justify-end gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => handleOpenChange(false)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading || projectsLoading}>
                {isLoading ? "Saving..." : isEditing ? "Update" : "Create"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}