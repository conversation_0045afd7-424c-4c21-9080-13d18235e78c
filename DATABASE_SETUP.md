# Database Setup & Management

This document outlines the database setup and management commands for the Smart AIO project.

## 🚀 Quick Start

For a fresh installation or complete reset:
```bash
npm run db:setup
```

## 📋 Available Commands

### Core Database Commands

| Command | Description |
|---------|-------------|
| `npm run db:setup` | **Complete setup**: Sync schema + Initialize + Seed data |
| `npm run db:reset` | **Reset database**: Drop all data + Apply migrations + Initialize |
| `npm run db:full-reset` | **Full reset**: Reset + Initialize + Seed test data |

### Individual Operations

| Command | Description |
|---------|-------------|
| `npm run db:generate` | Generate Prisma client |
| `npm run db:migrate` | Run database migrations (interactive) |
| `npm run db:push` | Push schema changes to database |
| `npm run db:sync` | Sync current schema to database (force) |
| `npm run db:init` | Initialize database with default data |
| `npm run db:seed` | Add test data for Q&A module |

## 🔄 Automatic Initialization

The system is configured to automatically initialize the database with essential data whenever a reset occurs:

### Default User (Always Created/Updated)
- **Username**: `trungbq`
- **Email**: `<EMAIL>`
- **Password**: `122333444`
- **Role**: `SUPER_ADMIN`
- **Full Name**: `Smith`

### Default Statuses
- Active
- Pending  
- On hold
- KIV
- Done

### Default Project
- **Code**: `DEFAULT_PROJECT`
- **Name**: `Default Project`
- **Customer**: `Default Customer`
- **Duration**: 12 months

### Default Modules
- Authentication
- User Management
- Dashboard
- Reports

## 🎯 Use Cases

### First Time Setup
```bash
npm install
npm run db:setup
```

### Development Reset (Keep Structure)
```bash
npm run db:init
```

### Complete Database Reset
```bash
npm run db:full-reset
```

### Schema Changes
```bash
# After modifying prisma/schema.prisma
npm run db:push
npm run db:init  # Re-initialize if needed
```

### Add Test Data
```bash
npm run db:seed
```

## 🔧 Technical Details

### User Validation
The initialization script uses `upsert` operations to ensure:
- The `trungbq` user exists before any operations
- No duplicate data is created on multiple runs
- Existing data is updated rather than causing conflicts

### Auto-Initialization Trigger
The database reset command (`npm run db:reset`) automatically triggers initialization:
```bash
npx prisma migrate reset --force && npm run db:sync && npm run db:init
```

### Schema Synchronization
The `db:sync` command ensures the database matches the current Prisma schema:
```bash
npx prisma db push --accept-data-loss
```

## ⚠️ Important Notes

1. **Data Loss Warning**: `db:reset` and `db:full-reset` will delete ALL existing data
2. **User Security**: The default password should be changed in production
3. **Environment**: These commands work with the database specified in your `.env` file
4. **Permissions**: Ensure your database user has full permissions for schema changes

## 🐛 Troubleshooting

### Prisma Client Generation Issues
If you encounter permission errors during client generation:
```bash
# Stop any running processes first
npm run db:generate
```

### Migration Conflicts
If migrations are out of sync:
```bash
npm run db:sync  # Force sync current schema
```

### Clean Slate Reset
For a completely fresh start:
```bash
npm run db:full-reset
```

## 📝 Development Workflow

1. **Make schema changes** in `prisma/schema.prisma`
2. **Push changes**: `npm run db:push`
3. **Initialize/update data**: `npm run db:init`
4. **Add test data**: `npm run db:seed` (optional)
5. **Test your changes** with the application

This ensures consistent database state across all development environments!