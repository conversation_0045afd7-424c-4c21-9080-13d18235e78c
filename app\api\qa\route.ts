import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const sortBy = searchParams.get("sortBy") || "Id";
    const sortOrder = searchParams.get("sortOrder") || "asc";
    const search = searchParams.get("search") || "";
    const project = searchParams.get("project") || "";
    
    const skip = (page - 1) * limit;
    
    const whereClause: Record<string, any> = {};
    
    // Add search filter
    if (search) {
      whereClause.OR = [
        { module: { Name: { contains: search, mode: "insensitive" as const } } },
        { Question: { contains: search, mode: "insensitive" as const } },
        { Answer: { contains: search, mode: "insensitive" as const } },
        { QuestionBy: { contains: search, mode: "insensitive" as const } },
        { AnswerBy: { contains: search, mode: "insensitive" as const } },
      ];
    }
    
    // Add project filter
    if (project) {
      whereClause.project_id = parseInt(project);
    }

    const [qas, total] = await Promise.all([
      prisma.qA.findMany({
        where: whereClause,
        skip,
        take: limit,
        orderBy: sortBy === "module" 
          ? { module: { Name: sortOrder as "asc" | "desc" } }
          : sortBy === "project"
          ? { project: { ProjectName: sortOrder as "asc" | "desc" } }
          : { [sortBy]: sortOrder as "asc" | "desc" },
        include: {
          project: true,
          status: true,
          module: true,
        },
      }),
      prisma.qA.count({ where: whereClause }),
    ]);

    return NextResponse.json({
      data: qas,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Failed to fetch QAs:", error);
    return NextResponse.json(
      { error: "Failed to fetch QAs" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    
    const Question = formData.get("Question") as string;
    const QuestionBy = formData.get("QuestionBy") as string;
    const Answer = formData.get("Answer") as string;
    const AnswerBy = formData.get("AnswerBy") as string;
    const project_id = parseInt(formData.get("project_id") as string);
    const status_id = parseInt(formData.get("status_id") as string);
    const module_id = parseInt(formData.get("module_id") as string);
    
    // Get module name from the selected module
    const module = await prisma.module.findUnique({
      where: { Id: module_id },
      select: { Name: true }
    });
    
    if (!module) {
      return NextResponse.json(
        { error: "Invalid module selected" },
        { status: 400 }
      );
    }
    
    const ModuleName = module.Name;
    
    const questionImages: string[] = [];
    const answerImages: string[] = [];
    
    // Handle question images
    const questionFiles = formData.getAll("questionImages") as File[];
    for (const file of questionFiles) {
      if (file.size > 0) {
        if (file.size > 5 * 1024 * 1024) {
          return NextResponse.json(
            { error: "Image size must be less than 5MB" },
            { status: 400 }
          );
        }
        
        // Here you would typically upload to a cloud storage service
        // For now, we'll store a placeholder URL
        const fileName = `${Date.now()}-${file.name}`;
        questionImages.push(`/uploads/qa/${fileName}`);
      }
    }
    
    // Handle answer images
    const answerFiles = formData.getAll("answerImages") as File[];
    for (const file of answerFiles) {
      if (file.size > 0) {
        if (file.size > 5 * 1024 * 1024) {
          return NextResponse.json(
            { error: "Image size must be less than 5MB" },
            { status: 400 }
          );
        }
        
        const fileName = `${Date.now()}-${file.name}`;
        answerImages.push(`/uploads/qa/${fileName}`);
      }
    }

    if (!Question || !QuestionBy || !project_id || !status_id || !module_id) {
      return NextResponse.json(
        { error: "Question, QuestionBy, project_id, status_id, and module_id are required" },
        { status: 400 }
      );
    }

    const qaData: any = {
      ModuleName,
      Question,
      QuestionBy,
      project: { connect: { Id: project_id } },
      status: { connect: { Id: status_id } },
      module: { connect: { Id: module_id } },
      QuestionImages: questionImages,
    };

    if (Answer) {
      qaData.Answer = Answer;
      qaData.AnswerBy = AnswerBy;
      qaData.AnswerDateTime = new Date();
      qaData.AnswerImages = answerImages;
    }

    const qa = await prisma.qA.create({
      data: qaData,
      include: {
        project: true,
        status: true,
        module: true,
      },
    });

    return NextResponse.json(qa, { status: 201 });
  } catch (error) {
    console.error("Failed to create QA:", error);
    return NextResponse.json(
      { error: "Failed to create QA" },
      { status: 500 }
    );
  }
}