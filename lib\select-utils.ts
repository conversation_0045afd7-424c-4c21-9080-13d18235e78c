/**
 * Utility functions for Select components to prevent common errors
 */

/**
 * Ensures a value is safe to use as a SelectItem value
 * SelectItem cannot have empty string values
 */
export function safeSelectValue(value: string | number | undefined | null, fallback = "placeholder"): string {
  if (value === null || value === undefined || value === "") {
    return fallback;
  }
  return String(value);
}

/**
 * Generates safe placeholder values for disabled SelectItems
 */
export function createPlaceholderValue(type: string): string {
  return `placeholder-${type}-${Date.now()}`;
}

/**
 * Checks if a value is a placeholder value
 */
export function isPlaceholderValue(value: string): boolean {
  return value.startsWith("placeholder-") || 
         value === "loading" || 
         value === "no-data" ||
         value.startsWith("no-");
}

/**
 * Creates a safe SelectItem props object
 */
export interface SafeSelectItemProps {
  value: string;
  disabled?: boolean;
  children: React.ReactNode;
}

export function createSafeSelectItem(
  value: string | number | undefined | null,
  children: React.ReactNode,
  disabled = false
): SafeSelectItemProps {
  return {
    value: safeSelectValue(value, createPlaceholderValue("item")),
    disabled,
    children
  };
}