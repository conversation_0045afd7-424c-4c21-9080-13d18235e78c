# Smart AIO Admin System

A Next.js-based knowledge management system with Q&A functionality and admin features, built with modern UI components and responsive design.

## Project Overview

This is a Smart AIO knowledge management system with Q&A functionality and admin features. The system includes:

- **Q&A System**: Public access for questions and answers with full CRUD operations
- **Admin Interface**: Management of statuses, modules, users, and projects
- **Modern UI** with Shadcn/UI components
- **Mobile-responsive design**
- **Advanced features**: pagination, sorting, filtering, search
- **Professional UX**: toast notifications, confirmation dialogs, loading skeletons

## Tech Stack

- **Framework**: Next.js 15.4.6 with App Router
- **Database**: PostgreSQL with Prisma ORM
- **UI Library**: Shadcn/UI (Radix + Tailwind CSS)
- **Authentication**: NextAuth.js
- **State Management**: React hooks
- **Notifications**: Sonner (toast notifications)
- **Form Handling**: React Hook Form + Zod validation

## Project Structure

```
├── app/
│   ├── qa/
│   │   └── page.tsx             # Public Q&A page
│   ├── admin/
│   │   ├── layout.tsx           # Admin layout wrapper
│   │   └── status/
│   │       └── page.tsx         # Main status management page
│   ├── api/
│   │   ├── qa/
│   │   │   ├── route.ts         # Q&A GET, POST endpoints
│   │   │   ├── [id]/route.ts    # Q&A GET, PUT, DELETE by ID
│   │   │   └── dropdown-data/   # Dropdown data for forms
│   │   └── admin/
│   │       └── status/
│   │           ├── route.ts     # GET, POST endpoints
│   │           └── [id]/
│   │               └── route.ts # GET, PUT, DELETE by ID
│   ├── globals.css              # Global styles with custom CSS variables
│   ├── layout.tsx               # Root layout with Toaster
│   └── providers.tsx            # Context providers
├── components/
│   ├── ui/                      # Shadcn/UI components
│   ├── ConfirmDialog.tsx        # Custom confirmation dialog
│   ├── StatusForm.tsx           # Add/Edit status form modal
│   └── TableSkeleton.tsx        # Loading skeleton components
├── lib/
│   ├── auth.ts                  # NextAuth configuration
│   ├── utils.ts                 # Utility functions
│   └── generated/prisma/        # Generated Prisma client
├── prisma/
│   └── schema.prisma            # Database schema
└── middleware.ts                # Authentication middleware
```

## Database Schema

The system uses a PostgreSQL database with the following main models:

### Status Model
```prisma
model Status {
  Id         Int    @id @default(autoincrement())
  Code       String @unique
  StatusName String
  qas        QA[]
  @@map("statuses")
}
```

## Key Features Implemented

### 1. Q&A System (`/qa`)
- **CRUD Operations**: Create, Read, Update, Delete Q&As
- **Advanced Table**: Sortable columns, pagination, filtering
- **Search**: Real-time search by questions, answers, modules
- **Image Support**: Upload and manage question/answer images
- **Public Access**: Available to all authenticated users
- **Responsive Design**: Mobile-friendly interface

### 2. Admin Status Management (`/admin/status`)
- **CRUD Operations**: Create, Read, Update, Delete statuses
- **Advanced Table**: Sortable columns, pagination, filtering
- **Search**: Real-time search by code or name
- **Responsive Design**: Mobile-friendly interface

### 3. UI/UX Features
- **Custom Confirmation Dialog**: Replace browser confirm with styled dialog
- **Toast Notifications**: Success/error feedback using Sonner
- **Loading Skeletons**: Professional loading states
- **Mobile Responsive**: Optimized for all screen sizes

### 4. API Endpoints

#### Q&A Endpoints

#### GET `/api/qa`
- Pagination: `?page=1&limit=10`
- Sorting: `?sortBy=Id&sortOrder=asc`
- Search: `?search=question`

#### POST `/api/qa`
- Create new Q&A with images

#### PUT `/api/qa/[id]`
- Update existing Q&A

#### DELETE `/api/qa/[id]`
- Delete Q&A

#### GET `/api/qa/dropdown-data`
- Get projects, statuses, and modules for forms

#### Admin Status Endpoints

#### GET `/api/admin/status`
- Pagination: `?page=1&limit=10`
- Sorting: `?sortBy=Id&sortOrder=asc`
- Search: `?search=active`

#### POST `/api/admin/status`
```json
{
  "Code": "NEW_STATUS",
  "StatusName": "New Status Name"
}
```

#### PUT `/api/admin/status/[id]`
#### DELETE `/api/admin/status/[id]`

## Setup Instructions

### Prerequisites
- Node.js 18+
- PostgreSQL database
- Git

### Environment Variables
Create `.env` file:
```env
DATABASE_URL="postgresql://username:password@host:port/database"
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key"
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
```

### Installation
```bash
# Install dependencies
npm install

# Generate Prisma client
npx prisma generate

# Run database migrations
npx prisma migrate dev

# Start development server
npm run dev
```

### Seeding Data
```bash
node scripts/seed-status.js
```

## Common Commands

### Development
```bash
npm run dev          # Start dev server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
```

### Database
```bash
npx prisma generate  # Generate Prisma client
npx prisma migrate dev # Run migrations
npx prisma studio    # Open Prisma Studio
```

## Design System

### Colors
- **Primary**: #27A600 (Green)
- **UI Library**: Shadcn/UI with New York style
- **Dark Mode**: Supported

### Components
- Tables: Shadcn Table components
- Forms: React Hook Form + Zod validation
- Dialogs: Radix Dialog primitives
- Buttons: Shadcn Button variants
- Skeletons: Animated loading states

## API Testing

### Using cURL
```bash
# Get all statuses
curl "http://localhost:3002/api/admin/status?page=1&limit=10"

# Create status
curl -X POST "http://localhost:3002/api/admin/status" \
  -H "Content-Type: application/json" \
  -d '{"Code":"TEST","StatusName":"Test Status"}'

# Update status
curl -X PUT "http://localhost:3002/api/admin/status/1" \
  -H "Content-Type: application/json" \
  -d '{"Code":"UPDATED","StatusName":"Updated Status"}'

# Delete status
curl -X DELETE "http://localhost:3002/api/admin/status/1"
```

## Development Notes

### Authentication
- Uses NextAuth.js with Google OAuth
- Middleware protects all routes except auth, static files, and login
- For testing, temporarily disable auth by updating middleware patterns

### Mobile Optimization
- Responsive table with horizontal scroll
- Compact action buttons (icon-only on mobile)
- Stacked layout for pagination on small screens
- Touch-friendly button sizes

### Performance
- Skeleton loading for better perceived performance
- Efficient pagination with server-side filtering
- Optimized database queries with Prisma

## Troubleshooting

### Common Issues
1. **Port conflicts**: Dev server uses port 3002 if 3000 is busy
2. **Database connection**: Check DATABASE_URL format
3. **Prisma errors**: Run `npx prisma generate` after schema changes
4. **Authentication**: Ensure NEXTAUTH_URL matches your domain

### Testing Authentication
Temporarily disable auth by updating `middleware.ts`:
```typescript
'/((?!api/auth|api/admin|admin|_next/static|_next/image|favicon.ico|login).*)'
```

## Future Enhancements

- Bulk operations (select multiple statuses)
- Export functionality (CSV, Excel)
- Audit logging for changes
- Advanced filtering options
- Status history tracking

## Support

For development assistance, ensure you have:
- Database connection working
- All environment variables set
- Latest dependencies installed
- Prisma client generated