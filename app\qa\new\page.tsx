"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useSearchPara<PERSON> } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";
import { Upload, X, Image, ArrowLeft, Save } from "lucide-react";
import { useClientOnly } from "@/hooks/useClientOnly";

const formSchema = z.object({
  Question: z.string().min(1, "Question is required"),
  QuestionBy: z.string().min(1, "Question by is required"),
  Answer: z.string().optional(),
  AnswerBy: z.string().optional(),
  project_id: z.number().min(1, "Project is required"),
  status_id: z.number().min(1, "Status is required"),
  module_id: z.number().min(1, "Module is required"),
});

type FormValues = z.infer<typeof formSchema>;

interface Project {
  Id: number;
  ProjectName: string;
}

interface Status {
  Id: number;
  StatusName: string;
}

interface Module {
  Id: number;
  Name: string;
}

interface QA {
  Id: number;
  ModuleName: string;
  Question: string;
  QuestionBy: string;
  QuestionDateTime: string;
  Answer: string | null;
  AnswerBy: string | null;
  AnswerDateTime: string | null;
  ImageUrl: string | null;
  QuestionImages: string[];
  AnswerImages: string[];
  project_id: number;
  status_id: number;
  module_id: number;
  project: {
    Id: number;
    ProjectName: string;
  };
  status: {
    Id: number;
    StatusName: string;
  };
  module: {
    Id: number;
    Name: string;
  };
}

export default function NewQAPage() {
  const mounted = useClientOnly();
  const router = useRouter();
  const searchParams = useSearchParams();
  const editId = searchParams.get("edit");

  const [loading, setLoading] = useState(false);
  const [dataLoading, setDataLoading] = useState(false);
  const [projects, setProjects] = useState<Project[]>([]);
  const [statuses, setStatuses] = useState<Status[]>([]);
  const [modules, setModules] = useState<Module[]>([]);
  const [questionFiles, setQuestionFiles] = useState<File[]>([]);
  const [answerFiles, setAnswerFiles] = useState<File[]>([]);
  const [existingQuestionImages, setExistingQuestionImages] = useState<string[]>([]);
  const [existingAnswerImages, setExistingAnswerImages] = useState<string[]>([]);
  const [editingQA, setEditingQA] = useState<QA | null>(null);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      Question: "",
      QuestionBy: "",
      Answer: "",
      AnswerBy: "",
      project_id: 0,
      status_id: 0,
      module_id: 0,
    },
  });

  const fetchDropdownData = async () => {
    setDataLoading(true);
    try {
      const response = await fetch("/api/qa/dropdown-data");
      if (!response.ok) {
        throw new Error("Failed to fetch dropdown data");
      }
      const data = await response.json();
      setProjects(data.projects);
      setStatuses(data.statuses);
      setModules(data.modules);
    } catch (error) {
      console.error("Failed to fetch dropdown data:", error);
      toast.error("Failed to load form data");
    } finally {
      setDataLoading(false);
    }
  };

  const fetchQAForEdit = async (id: string) => {
    try {
      const response = await fetch(`/api/qa/${id}`);
      if (!response.ok) {
        throw new Error("Failed to fetch QA");
      }
      const qa: QA = await response.json();
      setEditingQA(qa);

      form.reset({
        Question: qa.Question,
        QuestionBy: qa.QuestionBy,
        Answer: qa.Answer || "",
        AnswerBy: qa.AnswerBy || "",
        project_id: qa.project_id,
        status_id: qa.status_id,
        module_id: qa.module_id,
      });
      setExistingQuestionImages(qa.QuestionImages || []);
      setExistingAnswerImages(qa.AnswerImages || []);
    } catch (error) {
      console.error("Failed to fetch QA:", error);
      toast.error("Failed to load QA for editing");
      router.push("/qa");
    }
  };

  useEffect(() => {
    if (mounted) {
      fetchDropdownData();
      if (editId) {
        fetchQAForEdit(editId);
      }
    }
  }, [mounted, editId]);

  const handleQuestionFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setQuestionFiles(prev => [...prev, ...files]);
  };

  const handleAnswerFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setAnswerFiles(prev => [...prev, ...files]);
  };

  const removeQuestionFile = (index: number) => {
    setQuestionFiles(prev => prev.filter((_, i) => i !== index));
  };

  const removeAnswerFile = (index: number) => {
    setAnswerFiles(prev => prev.filter((_, i) => i !== index));
  };

  const removeExistingQuestionImage = (imageUrl: string) => {
    setExistingQuestionImages(prev => prev.filter(url => url !== imageUrl));
  };

  const removeExistingAnswerImage = (imageUrl: string) => {
    setExistingAnswerImages(prev => prev.filter(url => url !== imageUrl));
  };

  const onSubmit = async (values: FormValues) => {
    setLoading(true);
    try {
      const formData = new FormData();

      // Add form fields
      Object.entries(values).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          formData.append(key, value.toString());
        }
      });

      // Add question files
      questionFiles.forEach((file, index) => {
        formData.append(`questionImages`, file);
      });

      // Add answer files
      answerFiles.forEach((file, index) => {
        formData.append(`answerImages`, file);
      });

      // Add existing images that weren't removed
      if (existingQuestionImages.length > 0) {
        formData.append('existingQuestionImages', JSON.stringify(existingQuestionImages));
      }
      if (existingAnswerImages.length > 0) {
        formData.append('existingAnswerImages', JSON.stringify(existingAnswerImages));
      }

      const url = editingQA ? `/api/qa/${editingQA.Id}` : "/api/qa";
      const method = editingQA ? "PUT" : "POST";

      const response = await fetch(url, {
        method,
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Failed to ${editingQA ? "update" : "create"} QA`);
      }

      toast.success(`QA ${editingQA ? "updated" : "created"} successfully!`);
      router.push("/qa");
    } catch (error) {
      console.error("Failed to submit QA:", error);
      toast.error(`Failed to ${editingQA ? "update" : "create"} QA. Please try again.`);
    } finally {
      setLoading(false);
    }
  };

  if (!mounted) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto p-4 sm:p-6 max-w-4xl">
          <div className="flex flex-col sm:flex-row sm:items-center gap-4 mb-6">
            <Button variant="outline" size="sm" disabled className="w-fit">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Q&A
            </Button>
            <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold">Loading...</h1>
          </div>
          <Card className="bg-white shadow-sm">
            <CardContent className="p-4 sm:p-6">
              <div className="animate-pulse space-y-4">
                <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                <div className="h-10 bg-gray-200 rounded"></div>
                <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                <div className="h-10 bg-gray-200 rounded"></div>
                <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                <div className="h-24 bg-gray-200 rounded"></div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto p-4 sm:p-6 max-w-4xl">
        {/* Mobile-optimized header */}
        <div className="flex flex-col sm:flex-row sm:items-center gap-4 mb-6">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push("/qa")}
            className="flex-shrink-0 w-fit"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Q&A
          </Button>
          <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold">
            {editingQA ? "Edit Question & Answer" : "Ask New Question"}
          </h1>
        </div>

        <Card className="bg-white shadow-sm">
          <CardHeader className="pb-4">
            <CardTitle className="text-lg sm:text-xl">
              {editingQA ? "Update Q&A Details" : "Q&A Details"}
            </CardTitle>
          </CardHeader>
          <CardContent className="px-4 sm:px-6">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                {/* Project, Module, Status Row - Mobile-first responsive */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  <FormField
                    control={form.control}
                    name="project_id"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Project *</FormLabel>
                        <Select
                          onValueChange={(value) => field.onChange(parseInt(value))}
                          value={field.value > 0 ? field.value.toString() : ""}
                          disabled={dataLoading}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder={dataLoading ? "Loading..." : "Select project"} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {projects.map((project) => (
                              <SelectItem key={project.Id} value={project.Id.toString()}>
                                {project.ProjectName}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="module_id"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Module *</FormLabel>
                        <Select
                          onValueChange={(value) => field.onChange(parseInt(value))}
                          value={field.value > 0 ? field.value.toString() : ""}
                          disabled={dataLoading}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder={dataLoading ? "Loading..." : "Select module"} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {modules.map((module) => (
                              <SelectItem key={module.Id} value={module.Id.toString()}>
                                {module.Name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="status_id"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Status *</FormLabel>
                        <Select
                          onValueChange={(value) => field.onChange(parseInt(value))}
                          value={field.value > 0 ? field.value.toString() : ""}
                          disabled={dataLoading}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder={dataLoading ? "Loading..." : "Select status"} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {statuses.map((status) => (
                              <SelectItem key={status.Id} value={status.Id.toString()}>
                                {status.StatusName}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Question Section */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold border-b pb-2">Question Details</h3>

                  <div className="grid grid-cols-1 gap-4">
                    <FormField
                      control={form.control}
                      name="QuestionBy"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Asked By *</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter your name" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="Question"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Question *</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter your question here..."
                            className="min-h-[120px] resize-y"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Question Images */}
                  <div className="space-y-2">
                    <FormLabel>Question Images</FormLabel>
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 sm:p-6">
                      <div className="text-center">
                        <Upload className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                        <div className="text-sm text-gray-600 mb-2">
                          <span className="hidden sm:inline">Click to upload images or drag and drop</span>
                          <span className="sm:hidden">Tap to upload images</span>
                        </div>
                        <input
                          type="file"
                          multiple
                          accept="image/*"
                          onChange={handleQuestionFileChange}
                          className="hidden"
                          id="question-images"
                        />
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => document.getElementById('question-images')?.click()}
                          className="w-full sm:w-auto"
                        >
                          Choose Files
                        </Button>
                      </div>
                    </div>

                    {/* Display existing question images */}
                    {existingQuestionImages.length > 0 && (
                      <div className="space-y-2">
                        <div className="text-sm font-medium">Existing Images:</div>
                        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2">
                          {existingQuestionImages.map((imageUrl, index) => (
                            <div key={index} className="relative group">
                              <img
                                src={imageUrl}
                                alt={`Question image ${index + 1}`}
                                className="w-full h-20 object-cover rounded border"
                              />
                              <Button
                                type="button"
                                variant="destructive"
                                size="sm"
                                className="absolute top-1 right-1 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                                onClick={() => removeExistingQuestionImage(imageUrl)}
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Display new question files */}
                    {questionFiles.length > 0 && (
                      <div className="space-y-2">
                        <div className="text-sm font-medium">New Images:</div>
                        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2">
                          {questionFiles.map((file, index) => (
                            <div key={index} className="relative group">
                              <div className="w-full h-20 bg-gray-100 rounded border flex items-center justify-center">
                                <Image className="h-6 w-6 text-gray-400" />
                              </div>
                              <div className="text-xs text-center mt-1 truncate">{file.name}</div>
                              <Button
                                type="button"
                                variant="destructive"
                                size="sm"
                                className="absolute top-1 right-1 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                                onClick={() => removeQuestionFile(index)}
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Answer Section */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold border-b pb-2">Answer Details (Optional)</h3>

                  <div className="grid grid-cols-1 gap-4">
                    <FormField
                      control={form.control}
                      name="AnswerBy"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Answered By</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter answerer's name" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="Answer"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Answer</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter the answer here..."
                            className="min-h-[120px] resize-y"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Answer Images */}
                  <div className="space-y-2">
                    <FormLabel>Answer Images</FormLabel>
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 sm:p-6">
                      <div className="text-center">
                        <Upload className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                        <div className="text-sm text-gray-600 mb-2">
                          <span className="hidden sm:inline">Click to upload images or drag and drop</span>
                          <span className="sm:hidden">Tap to upload images</span>
                        </div>
                        <input
                          type="file"
                          multiple
                          accept="image/*"
                          onChange={handleAnswerFileChange}
                          className="hidden"
                          id="answer-images"
                        />
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => document.getElementById('answer-images')?.click()}
                          className="w-full sm:w-auto"
                        >
                          Choose Files
                        </Button>
                      </div>
                    </div>

                    {/* Display existing answer images */}
                    {existingAnswerImages.length > 0 && (
                      <div className="space-y-2">
                        <div className="text-sm font-medium">Existing Images:</div>
                        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2">
                          {existingAnswerImages.map((imageUrl, index) => (
                            <div key={index} className="relative group">
                              <img
                                src={imageUrl}
                                alt={`Answer image ${index + 1}`}
                                className="w-full h-20 object-cover rounded border"
                              />
                              <Button
                                type="button"
                                variant="destructive"
                                size="sm"
                                className="absolute top-1 right-1 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                                onClick={() => removeExistingAnswerImage(imageUrl)}
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Display new answer files */}
                    {answerFiles.length > 0 && (
                      <div className="space-y-2">
                        <div className="text-sm font-medium">New Images:</div>
                        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2">
                          {answerFiles.map((file, index) => (
                            <div key={index} className="relative group">
                              <div className="w-full h-20 bg-gray-100 rounded border flex items-center justify-center">
                                <Image className="h-6 w-6 text-gray-400" />
                              </div>
                              <div className="text-xs text-center mt-1 truncate">{file.name}</div>
                              <Button
                                type="button"
                                variant="destructive"
                                size="sm"
                                className="absolute top-1 right-1 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                                onClick={() => removeAnswerFile(index)}
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Form Actions - Mobile-optimized */}
                <div className="flex flex-col-reverse sm:flex-row gap-3 pt-6 border-t">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => router.push("/qa")}
                    className="w-full sm:w-auto"
                    disabled={loading}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={loading || dataLoading}
                    className="w-full sm:w-auto sm:ml-auto"
                  >
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        {editingQA ? "Updating..." : "Creating..."}
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        {editingQA ? "Update Q&A" : "Create Q&A"}
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
      );
}
