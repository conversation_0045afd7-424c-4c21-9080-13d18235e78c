const { PrismaClient } = require('../lib/generated/prisma');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function createTestUser() {
  try {
    const hashedPassword = await bcrypt.hash('testpass123', 12);
    
    const user = await prisma.user.create({
      data: {
        user_name: 'testuser',
        email: '<EMAIL>',
        password: hashedPassword,
        full_name: 'Test User',
        name: 'Test User',
        role: 'SUPER_ADMIN'
      }
    });

    console.log('Test user created successfully:', user);
  } catch (error) {
    if (error.code === 'P2002') {
      console.log('Test user already exists');
    } else {
      console.error('Error creating test user:', error);
    }
  } finally {
    await prisma.$disconnect();
  }
}

createTestUser();