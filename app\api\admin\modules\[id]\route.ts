import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: idParam } = await params;
    const id = parseInt(idParam);
    
    if (isNaN(id)) {
      return NextResponse.json(
        { error: "Invalid ID" },
        { status: 400 }
      );
    }

    const foundModule = await prisma.module.findUnique({
      where: { Id: id },
      include: {
        project: {
          select: {
            Id: true,
            Code: true,
            ProjectName: true,
          }
        }
      }
    });

    if (!foundModule) {
      return NextResponse.json(
        { error: "Module not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(foundModule);
  } catch (error) {
    console.error("Error fetching module:", error);
    return NextResponse.json(
      { error: "Failed to fetch module" },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: idParam } = await params;
    const id = parseInt(idParam);
    const { Code, Name, project_id } = await request.json();

    if (isNaN(id)) {
      return NextResponse.json(
        { error: "Invalid ID" },
        { status: 400 }
      );
    }

    if (!Code || !Name || !project_id) {
      return NextResponse.json(
        { error: "Code, Name, and project_id are required" },
        { status: 400 }
      );
    }

    // Check if project exists
    const project = await prisma.project.findUnique({
      where: { Id: project_id }
    });

    if (!project) {
      return NextResponse.json(
        { error: "Project not found" },
        { status: 404 }
      );
    }

    const updatedModule = await prisma.module.update({
      where: { Id: id },
      data: {
        Code,
        Name,
        project_id,
      },
      include: {
        project: {
          select: {
            Id: true,
            Code: true,
            ProjectName: true,
          }
        }
      }
    });

    return NextResponse.json(updatedModule);
  } catch (error) {
    console.error("Error updating module:", error);
    if (error && typeof error === 'object' && 'code' in error && error.code === 'P2002') {
      return NextResponse.json(
        { error: "Module code already exists" },
        { status: 409 }
      );
    }
    if (error && typeof error === 'object' && 'code' in error && error.code === 'P2025') {
      return NextResponse.json(
        { error: "Module not found" },
        { status: 404 }
      );
    }
    return NextResponse.json(
      { error: "Failed to update module" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: idParam } = await params;
    const id = parseInt(idParam);
    
    if (isNaN(id)) {
      return NextResponse.json(
        { error: "Invalid ID" },
        { status: 400 }
      );
    }

    // Check if module has related QAs
    const relatedQAs = await prisma.qA.count({
      where: { module_id: id }
    });

    if (relatedQAs > 0) {
      return NextResponse.json(
        { error: `Cannot delete module. It has ${relatedQAs} related Q&As.` },
        { status: 409 }
      );
    }

    await prisma.module.delete({
      where: { Id: id },
    });

    return NextResponse.json({ message: "Module deleted successfully" });
  } catch (error) {
    console.error("Error deleting module:", error);
    if (error && typeof error === 'object' && 'code' in error && error.code === 'P2025') {
      return NextResponse.json(
        { error: "Module not found" },
        { status: 404 }
      );
    }
    return NextResponse.json(
      { error: "Failed to delete module" },
      { status: 500 }
    );
  }
}