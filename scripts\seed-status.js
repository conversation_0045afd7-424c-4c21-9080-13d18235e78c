const { PrismaClient } = require('../lib/generated/prisma');

const prisma = new PrismaClient();

async function main() {
  console.log('Seeding status data...');
  
  const statuses = [
    { Code: 'ACTIVE', StatusName: 'Active' },
    { Code: 'INACTIVE', StatusName: 'Inactive' },
    { Code: 'PENDING', StatusName: 'Pending' },
    { Code: 'COMPLETED', StatusName: 'Completed' },
    { Code: 'CANCELLED', StatusName: 'Cancelled' },
    { Code: 'DRAFT', StatusName: 'Draft' },
    { Code: 'REVIEW', StatusName: 'Under Review' },
    { Code: 'APPROVED', StatusName: 'Approved' },
    { Code: 'REJECTED', StatusName: 'Rejected' },
    { Code: 'ARCHIVED', StatusName: 'Archived' },
  ];

  for (const status of statuses) {
    try {
      await prisma.status.upsert({
        where: { Code: status.Code },
        update: {},
        create: status,
      });
      console.log(`Created/Updated status: ${status.Code}`);
    } catch (error) {
      console.log(`Status ${status.Code} might already exist`);
    }
  }
  
  console.log('Seeding completed!');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });